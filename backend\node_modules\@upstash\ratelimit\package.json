{"name": "@upstash/ratelimit", "version": "v2.0.5", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "test": "bun test src --coverage", "fmt": "prettier --write .", "lint": "eslint \"src/**/*.{js,ts,tsx}\" --quiet --fix"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.4.0", "bun-types": "latest", "eslint": "^9.10.0", "eslint-plugin-unicorn": "^55.0.0", "tsup": "^7.2.0", "turbo": "^1.10.15", "typescript": "^5.0.0"}, "peerDependencies": {"@upstash/redis": "^1.34.3"}, "license": "MIT", "dependencies": {"@upstash/core-analytics": "^0.0.10"}}