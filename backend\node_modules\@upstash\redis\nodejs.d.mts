import { R as RedisOptions, a as RequesterConfig, b as Redis$1 } from './zmscore-CjoCv9kz.mjs';
export { A as AppendCommand, B as BitCountCommand, f as BitOpCommand, g as BitPosCommand, C as CopyCommand, D as DBSizeCommand, i as DecrByCommand, h as DecrCommand, j as DelCommand, E as EchoCommand, l as EvalCommand, k as EvalROCommand, n as EvalshaCommand, m as EvalshaROCommand, o as ExistsCommand, r as ExpireAtCommand, p as ExpireCommand, q as ExpireOption, F as FlushAllCommand, s as FlushDBCommand, G as GeoAddCommand, t as GeoAddCommandOptions, v as GeoDistCommand, w as GeoHashCommand, u as GeoMember, x as GeoPosCommand, y as GeoSearchCommand, z as GeoSearchStoreCommand, I as GetBitCommand, H as GetCommand, J as GetDelCommand, K as GetExCommand, L as Get<PERSON>ang<PERSON>Command, M as GetSetCommand, N as HDelCommand, O as HExistsCommand, S as HExpireAtCommand, Q as HExpireCommand, T as HExpireTimeCommand, a0 as HGetAllCommand, $ as HGetCommand, a1 as HIncrByCommand, a2 as HIncrByFloatCommand, a3 as HKeysCommand, a4 as HLenCommand, a5 as HMGetCommand, a6 as HMSetCommand, X as HPExpireAtCommand, W as HPExpireCommand, Y as HPExpireTimeCommand, Z as HPTtlCommand, _ as HPersistCommand, a7 as HRandFieldCommand, a8 as HScanCommand, a9 as HSetCommand, aa as HSetNXCommand, ab as HStrLenCommand, V as HTtlCommand, ac as HValsCommand, ae as IncrByCommand, af as IncrByFloatCommand, ad as IncrCommand, ag as JsonArrAppendCommand, ah as JsonArrIndexCommand, ai as JsonArrInsertCommand, aj as JsonArrLenCommand, ak as JsonArrPopCommand, al as JsonArrTrimCommand, am as JsonClearCommand, an as JsonDelCommand, ao as JsonForgetCommand, ap as JsonGetCommand, ar as JsonMGetCommand, aq as JsonMergeCommand, as as JsonNumIncrByCommand, at as JsonNumMultByCommand, au as JsonObjKeysCommand, av as JsonObjLenCommand, aw as JsonRespCommand, ax as JsonSetCommand, ay as JsonStrAppendCommand, az as JsonStrLenCommand, aA as JsonToggleCommand, aB as JsonTypeCommand, aC as KeysCommand, aD as LIndexCommand, aE as LInsertCommand, aF as LLenCommand, aG as LMoveCommand, aH as LPopCommand, aI as LPushCommand, aJ as LPushXCommand, aK as LRangeCommand, aL as LRemCommand, aM as LSetCommand, aN as LTrimCommand, aO as MGetCommand, aP as MSetCommand, aQ as MSetNXCommand, aT as PExpireAtCommand, aS as PExpireCommand, aV as PSetEXCommand, aW as PTtlCommand, aR as PersistCommand, aU as PingCommand, P as Pipeline, aX as PublishCommand, a$ as RPopCommand, b0 as RPushCommand, b1 as RPushXCommand, aY as RandomKeyCommand, aZ as RenameCommand, a_ as RenameNXCommand, d as Requester, b2 as SAddCommand, b5 as SCardCommand, b9 as SDiffCommand, ba as SDiffStoreCommand, bh as SInterCommand, bi as SInterStoreCommand, bj as SIsMemberCommand, bl as SMIsMemberCommand, bk as SMembersCommand, bm as SMoveCommand, bn as SPopCommand, bo as SRandMemberCommand, bp as SRemCommand, bq as SScanCommand, bs as SUnionCommand, bt as SUnionStoreCommand, b3 as ScanCommand, b4 as ScanCommandOptions, bC as ScoreMember, b6 as ScriptExistsCommand, b7 as ScriptFlushCommand, b8 as ScriptLoadCommand, bd as SetBitCommand, bb as SetCommand, bc as SetCommandOptions, be as SetExCommand, bf as SetNxCommand, bg as SetRangeCommand, br as StrLenCommand, bu as TimeCommand, bv as TouchCommand, bw as TtlCommand, bx as Type, by as TypeCommand, bz as UnlinkCommand, U as UpstashRequest, c as UpstashResponse, bA as XAddCommand, bB as XRangeCommand, bE as ZAddCommand, bD as ZAddCommandOptions, bF as ZCardCommand, bG as ZCountCommand, bH as ZDiffStoreCommand, bI as ZIncrByCommand, bJ as ZInterStoreCommand, bK as ZInterStoreCommandOptions, bL as ZLexCountCommand, bM as ZMScoreCommand, bN as ZPopMaxCommand, bO as ZPopMinCommand, bP as ZRangeCommand, bQ as ZRangeCommandOptions, bR as ZRankCommand, bS as ZRemCommand, bT as ZRemRangeByLexCommand, bU as ZRemRangeByRankCommand, bV as ZRemRangeByScoreCommand, bW as ZRevRankCommand, bX as ZScanCommand, bY as ZScoreCommand, bZ as ZUnionCommand, b_ as ZUnionCommandOptions, b$ as ZUnionStoreCommand, c0 as ZUnionStoreCommandOptions, e as errors } from './zmscore-CjoCv9kz.mjs';

/**
 * Connection credentials for upstash redis.
 * Get them from https://console.upstash.com/redis/<uuid>
 */
type RedisConfigNodejs = {
    /**
     * UPSTASH_REDIS_REST_URL
     */
    url: string | undefined;
    /**
     * UPSTASH_REDIS_REST_TOKEN
     */
    token: string | undefined;
    /**
     * An agent allows you to reuse connections to reduce latency for multiple sequential requests.
     *
     * This is a node specific implementation and is not supported in various runtimes like Vercel
     * edge functions.
     *
     * @example
     * ```ts
     * import https from "https"
     *
     * const options: RedisConfigNodejs = {
     *  agent: new https.Agent({ keepAlive: true })
     * }
     * ```
     */
    /**
     * The signal will allow aborting requests on the fly.
     * For more check: https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal
     */
    signal?: AbortSignal;
    latencyLogging?: boolean;
    agent?: unknown;
    keepAlive?: boolean;
    /**
     * When this flag is enabled, any subsequent commands issued by this client are guaranteed to observe the effects of all earlier writes submitted by the same client.
     */
    readYourWrites?: boolean;
} & RedisOptions & RequesterConfig;
/**
 * Serverless redis client for upstash.
 */
declare class Redis extends Redis$1 {
    /**
     * Create a new redis client by providing the url and token
     *
     * @example
     * ```typescript
     * const redis = new Redis({
     *  url: "<UPSTASH_REDIS_REST_URL>",
     *  token: "<UPSTASH_REDIS_REST_TOKEN>",
     * });
     * ```
     */
    constructor(config: RedisConfigNodejs);
    /**
     * Create a new Upstash Redis instance from environment variables.
     *
     * Use this to automatically load connection secrets from your environment
     * variables. For instance when using the Vercel integration.
     *
     * This tries to load `UPSTASH_REDIS_REST_URL` and `UPSTASH_REDIS_REST_TOKEN` from
     * your environment using `process.env`.
     */
    static fromEnv(config?: Omit<RedisConfigNodejs, "url" | "token">): Redis;
}

export { Redis, type RedisConfigNodejs };
