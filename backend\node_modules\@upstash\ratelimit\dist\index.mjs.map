{"version": 3, "sources": ["../src/analytics.ts", "../src/cache.ts", "../src/duration.ts", "../src/hash.ts", "../src/lua-scripts/single.ts", "../src/lua-scripts/multi.ts", "../src/lua-scripts/reset.ts", "../src/lua-scripts/hash.ts", "../src/types.ts", "../src/deny-list/scripts.ts", "../src/deny-list/ip-deny-list.ts", "../src/deny-list/time.ts", "../src/deny-list/deny-list.ts", "../src/ratelimit.ts", "../src/multi.ts", "../src/single.ts"], "sourcesContent": ["import type { Aggregate } from \"@upstash/core-analytics\";\nimport { Analytics as CoreAnalytics } from \"@upstash/core-analytics\";\nimport type { Redis } from \"./types\";\n\nexport type Geo = {\n  country?: string;\n  city?: string;\n  region?: string;\n  ip?: string;\n};\n\n/**\n * denotes the success field in the analytics submission.\n * Set to true when ratelimit check passes. False when request is ratelimited.\n * Set to \"denied\" when some request value is in deny list.\n */\nexport type EventSuccess = boolean | \"denied\"\n\nexport type Event = Geo & {\n  identifier: string;\n  time: number;\n  success: EventSuccess;\n};\n\nexport type AnalyticsConfig = {\n  redis: Redis;\n  prefix?: string;\n};\n\n/**\n * The Analytics package is experimental and can change at any time.\n */\nexport class Analytics {\n  private readonly analytics: CoreAnalytics;\n  private readonly table = \"events\";\n\n  constructor(config: AnalyticsConfig) {\n    this.analytics = new CoreAnalytics({\n      // @ts-expect-error we need to fix the types in core-analytics, it should only require the methods it needs, not the whole sdk\n      redis: config.redis,\n      window: \"1h\",\n      prefix: config.prefix ?? \"@upstash/ratelimit\",\n      retention: \"90d\",\n    });\n  }\n\n  /**\n   * Try to extract the geo information from the request\n   *\n   * This handles Vercel's `req.geo` and  and Cloudflare's `request.cf` properties\n   * @param req\n   * @returns\n   */\n  public extractGeo(req: { geo?: Geo; cf?: Geo }): Geo {\n    if (req.geo !== undefined) {\n      return req.geo;\n    }\n    if (req.cf !== undefined) {\n      return req.cf;\n    }\n\n    return {};\n  }\n\n  public async record(event: Event): Promise<void> {\n    await this.analytics.ingest(this.table, event);\n  }\n\n  public async series<TFilter extends keyof Omit<Event, \"time\">>(\n    filter: TFilter,\n    cutoff: number,\n  ): Promise<Aggregate[]> {\n    const timestampCount = Math.min(\n      (\n        this.analytics.getBucket(Date.now())\n        - this.analytics.getBucket(cutoff)\n      ) / (60 * 60 * 1000),\n      256\n    )\n    return this.analytics.aggregateBucketsWithPipeline(this.table, filter, timestampCount)\n  }\n\n  public async getUsage(cutoff = 0): Promise<Record<string, { success: number; blocked: number }>> {\n    \n    const timestampCount = Math.min(\n      (\n        this.analytics.getBucket(Date.now())\n        - this.analytics.getBucket(cutoff)\n      ) / (60 * 60 * 1000),\n      256\n    )\n    const records = await this.analytics.getAllowedBlocked(this.table, timestampCount)\n    return records;\n  }\n\n  public async getUsageOverTime<TFilter extends keyof Omit<Event, \"time\">>(\n    timestampCount: number, groupby: TFilter\n  ): Promise<Aggregate[]> {\n    const result = await this.analytics.aggregateBucketsWithPipeline(this.table, groupby, timestampCount)\n    return result\n  }\n\n  public async getMostAllowedBlocked(timestampCount: number, getTop?: number, checkAtMost?: number) {\n    getTop = getTop ?? 5\n    const timestamp = undefined // let the analytics handle getting the timestamp\n    return this.analytics.getMostAllowedBlocked(this.table, timestampCount, getTop, timestamp, checkAtMost)\n  }\n}\n", "import type { EphemeralC<PERSON> } from \"./types\";\n\nexport class <PERSON>ache implements EphemeralCache {\n  /**\n   * Stores identifier -> reset (in milliseconds)\n   */\n  private readonly cache: Map<string, number>;\n\n  constructor(cache: Map<string, number>) {\n    this.cache = cache;\n  }\n\n  public isBlocked(identifier: string): { blocked: boolean; reset: number } {\n    if (!this.cache.has(identifier)) {\n      return { blocked: false, reset: 0 };\n    }\n    const reset = this.cache.get(identifier)!;\n    if (reset < Date.now()) {\n      this.cache.delete(identifier);\n      return { blocked: false, reset: 0 };\n    }\n\n    return { blocked: true, reset: reset };\n  }\n\n  public blockUntil(identifier: string, reset: number): void {\n    this.cache.set(identifier, reset);\n  }\n\n  public set(key: string, value: number): void {\n    this.cache.set(key, value);\n  }\n  public get(key: string): number | null {\n    return this.cache.get(key) || null;\n  }\n\n  public incr(key: string): number {\n    let value = this.cache.get(key) ?? 0;\n    value += 1;\n    this.cache.set(key, value);\n    return value;\n  }\n\n  public pop(key: string): void {\n    this.cache.delete(key)\n  }\n\n  public empty(): void {\n    this.cache.clear()\n  }\n\n  public size(): number {\n    return this.cache.size;\n  }\n}\n", "type Unit = \"ms\" | \"s\" | \"m\" | \"h\" | \"d\";\nexport type Duration = `${number} ${Unit}` | `${number}${Unit}`;\n\n/**\n * Convert a human readable duration to milliseconds\n */\nexport function ms(d: Duration): number {\n  const match = d.match(/^(\\d+)\\s?(ms|s|m|h|d)$/);\n  if (!match) {\n    throw new Error(`Unable to parse window size: ${d}`);\n  }\n  const time = Number.parseInt(match[1]);\n  const unit = match[2] as Unit;\n\n  switch (unit) {\n    case \"ms\": {\n      return time;\n    }\n    case \"s\": {\n      return time * 1000;\n    }\n    case \"m\": {\n      return time * 1000 * 60;\n    }\n    case \"h\": {\n      return time * 1000 * 60 * 60;\n    }\n    case \"d\": {\n      return time * 1000 * 60 * 60 * 24;\n    }\n\n    default: {\n      throw new Error(`Unable to parse window size: ${d}`);\n    }\n  }\n}\n", "import type { ScriptInfo } from \"./lua-scripts/hash\";\nimport type { RegionContext } from \"./types\";\n\n/**\n * Runs the specified script with EVALSHA using the scriptHash parameter.\n * \n * If the EVALSHA fails, loads the script to redis and runs again with the\n * hash returned from Redis.\n * \n * @param ctx Regional or multi region context\n * @param script ScriptInfo of script to run. Contains the script and its hash\n * @param keys eval keys\n * @param args eval args\n */\nexport const safeEval = async (\n  ctx: RegionContext,\n  script: ScriptInfo,\n  keys: any[],\n  args: any[],\n) => {\n  try {\n    return await ctx.redis.evalsha(script.hash, keys, args)\n  } catch (error) {\n    if (`${error}`.includes(\"NOSCRIPT\")) {\n      const hash = await ctx.redis.scriptLoad(script.script)\n\n      if (hash !== script.hash) {\n        console.warn(\n          \"Upstash Ratelimit: Expected hash and the hash received from Redis\"\n          + \" are different. Ratelimit will work as usual but performance will\"\n          + \" be reduced.\"\n        );\n      }\n\n      return await ctx.redis.evalsha(hash, keys, args)\n    }\n    throw error;\n  }\n}", "export const fixedWindowLimitScript = `\n  local key           = KEYS[1]\n  local window        = ARGV[1]\n  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1\n\n  local r = redis.call(\"INCRBY\", key, incrementBy)\n  if r == tonumber(incrementBy) then\n  -- The first time this key is set, the value will be equal to incrementBy.\n  -- So we only need the expire command once\n  redis.call(\"PEXPIRE\", key, window)\n  end\n\n  return r\n`;\n\nexport const fixedWindowRemainingTokensScript = `\n      local key = KEYS[1]\n      local tokens = 0\n\n      local value = redis.call('GET', key)\n      if value then\n          tokens = value\n      end\n      return tokens\n    `;\n\nexport const slidingWindowLimitScript = `\n  local currentKey  = KEYS[1]           -- identifier including prefixes\n  local previousKey = KEYS[2]           -- key of the previous bucket\n  local tokens      = tonumber(ARGV[1]) -- tokens per window\n  local now         = ARGV[2]           -- current timestamp in milliseconds\n  local window      = ARGV[3]           -- interval in milliseconds\n  local incrementBy = ARGV[4]           -- increment rate per request at a given value, default is 1\n\n  local requestsInCurrentWindow = redis.call(\"GET\", currentKey)\n  if requestsInCurrentWindow == false then\n    requestsInCurrentWindow = 0\n  end\n\n  local requestsInPreviousWindow = redis.call(\"GET\", previousKey)\n  if requestsInPreviousWindow == false then\n    requestsInPreviousWindow = 0\n  end\n  local percentageInCurrent = ( now % window ) / window\n  -- weighted requests to consider from the previous window\n  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n  if requestsInPreviousWindow + requestsInCurrentWindow >= tokens then\n    return -1\n  end\n\n  local newValue = redis.call(\"INCRBY\", currentKey, incrementBy)\n  if newValue == tonumber(incrementBy) then\n    -- The first time this key is set, the value will be equal to incrementBy.\n    -- So we only need the expire command once\n    redis.call(\"PEXPIRE\", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second\n  end\n  return tokens - ( newValue + requestsInPreviousWindow )\n`;\n\nexport const slidingWindowRemainingTokensScript = `\n  local currentKey  = KEYS[1]           -- identifier including prefixes\n  local previousKey = KEYS[2]           -- key of the previous bucket\n  local now         = ARGV[1]           -- current timestamp in milliseconds\n  local window      = ARGV[2]           -- interval in milliseconds\n\n  local requestsInCurrentWindow = redis.call(\"GET\", currentKey)\n  if requestsInCurrentWindow == false then\n    requestsInCurrentWindow = 0\n  end\n\n  local requestsInPreviousWindow = redis.call(\"GET\", previousKey)\n  if requestsInPreviousWindow == false then\n    requestsInPreviousWindow = 0\n  end\n\n  local percentageInCurrent = ( now % window ) / window\n  -- weighted requests to consider from the previous window\n  requestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n\n  return requestsInPreviousWindow + requestsInCurrentWindow\n`;\n\nexport const tokenBucketLimitScript = `\n  local key         = KEYS[1]           -- identifier including prefixes\n  local maxTokens   = tonumber(ARGV[1]) -- maximum number of tokens\n  local interval    = tonumber(ARGV[2]) -- size of the window in milliseconds\n  local refillRate  = tonumber(ARGV[3]) -- how many tokens are refilled after each interval\n  local now         = tonumber(ARGV[4]) -- current timestamp in milliseconds\n  local incrementBy = tonumber(ARGV[5]) -- how many tokens to consume, default is 1\n        \n  local bucket = redis.call(\"HMGET\", key, \"refilledAt\", \"tokens\")\n        \n  local refilledAt\n  local tokens\n\n  if bucket[1] == false then\n    refilledAt = now\n    tokens = maxTokens\n  else\n    refilledAt = tonumber(bucket[1])\n    tokens = tonumber(bucket[2])\n  end\n        \n  if now >= refilledAt + interval then\n    local numRefills = math.floor((now - refilledAt) / interval)\n    tokens = math.min(maxTokens, tokens + numRefills * refillRate)\n\n    refilledAt = refilledAt + numRefills * interval\n  end\n\n  if tokens == 0 then\n    return {-1, refilledAt + interval}\n  end\n\n  local remaining = tokens - incrementBy\n  local expireAt = math.ceil(((maxTokens - remaining) / refillRate)) * interval\n        \n  redis.call(\"HSET\", key, \"refilledAt\", refilledAt, \"tokens\", remaining)\n  redis.call(\"PEXPIRE\", key, expireAt)\n  return {remaining, refilledAt + interval}\n`;\n\nexport const tokenBucketIdentifierNotFound = -1\n\nexport const tokenBucketRemainingTokensScript = `\n  local key         = KEYS[1]\n  local maxTokens   = tonumber(ARGV[1])\n        \n  local bucket = redis.call(\"HMGET\", key, \"refilledAt\", \"tokens\")\n\n  if bucket[1] == false then\n    return {maxTokens, ${tokenBucketIdentifierNotFound}}\n  end\n        \n  return {tonumber(bucket[2]), tonumber(bucket[1])}\n`;\n\nexport const cachedFixedWindowLimitScript = `\n  local key     = KEYS[1]\n  local window  = ARGV[1]\n  local incrementBy   = ARGV[2] -- increment rate per request at a given value, default is 1\n\n  local r = redis.call(\"INCRBY\", key, incrementBy)\n  if r == incrementBy then\n  -- The first time this key is set, the value will be equal to incrementBy.\n  -- So we only need the expire command once\n  redis.call(\"PEXPIRE\", key, window)\n  end\n      \n  return r\n`;\n\nexport const cachedFixedWindowRemainingTokenScript = `\n  local key = KEYS[1]\n  local tokens = 0\n\n  local value = redis.call('GET', key)\n  if value then\n      tokens = value\n  end\n  return tokens\n`;\n", "export const fixedWindowLimitScript = `\n\tlocal key           = KEYS[1]\n\tlocal id            = ARGV[1]\n\tlocal window        = ARGV[2]\n\tlocal incrementBy   = tonumber(ARGV[3])\n\n\tredis.call(\"HSET\", key, id, incrementBy)\n\tlocal fields = redis.call(\"HGETALL\", key)\n\tif #fields == 2 and tonumber(fields[2])==incrementBy then\n\t-- The first time this key is set, and the value will be equal to incrementBy.\n\t-- So we only need the expire command once\n\t  redis.call(\"PEXPIRE\", key, window)\n\tend\n\n\treturn fields\n`;\nexport const fixedWindowRemainingTokensScript = `\n      local key = KEYS[1]\n      local tokens = 0\n\n      local fields = redis.call(\"HGETALL\", key)\n\n      return fields\n    `;\n\nexport const slidingWindowLimitScript = `\n\tlocal currentKey    = KEYS[1]           -- identifier including prefixes\n\tlocal previousKey   = KEYS[2]           -- key of the previous bucket\n\tlocal tokens        = tonumber(ARGV[1]) -- tokens per window\n\tlocal now           = ARGV[2]           -- current timestamp in milliseconds\n\tlocal window        = ARGV[3]           -- interval in milliseconds\n\tlocal requestId     = ARGV[4]           -- uuid for this request\n\tlocal incrementBy   = tonumber(ARGV[5]) -- custom rate, default is  1\n\n\tlocal currentFields = redis.call(\"HGETALL\", currentKey)\n\tlocal requestsInCurrentWindow = 0\n\tfor i = 2, #currentFields, 2 do\n\trequestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])\n\tend\n\n\tlocal previousFields = redis.call(\"HGETALL\", previousKey)\n\tlocal requestsInPreviousWindow = 0\n\tfor i = 2, #previousFields, 2 do\n\trequestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])\n\tend\n\n\tlocal percentageInCurrent = ( now % window) / window\n\tif requestsInPreviousWindow * (1 - percentageInCurrent ) + requestsInCurrentWindow >= tokens then\n\t  return {currentFields, previousFields, false}\n\tend\n\n\tredis.call(\"HSET\", currentKey, requestId, incrementBy)\n\n\tif requestsInCurrentWindow == 0 then \n\t  -- The first time this key is set, the value will be equal to incrementBy.\n\t  -- So we only need the expire command once\n\t  redis.call(\"PEXPIRE\", currentKey, window * 2 + 1000) -- Enough time to overlap with a new window + 1 second\n\tend\n\treturn {currentFields, previousFields, true}\n`;\n\nexport const slidingWindowRemainingTokensScript = `\n\tlocal currentKey    = KEYS[1]           -- identifier including prefixes\n\tlocal previousKey   = KEYS[2]           -- key of the previous bucket\n\tlocal now         \t= ARGV[1]           -- current timestamp in milliseconds\n  \tlocal window      \t= ARGV[2]           -- interval in milliseconds\n\n\tlocal currentFields = redis.call(\"HGETALL\", currentKey)\n\tlocal requestsInCurrentWindow = 0\n\tfor i = 2, #currentFields, 2 do\n\trequestsInCurrentWindow = requestsInCurrentWindow + tonumber(currentFields[i])\n\tend\n\n\tlocal previousFields = redis.call(\"HGETALL\", previousKey)\n\tlocal requestsInPreviousWindow = 0\n\tfor i = 2, #previousFields, 2 do\n\trequestsInPreviousWindow = requestsInPreviousWindow + tonumber(previousFields[i])\n\tend\n\n\tlocal percentageInCurrent = ( now % window) / window\n  \trequestsInPreviousWindow = math.floor(( 1 - percentageInCurrent ) * requestsInPreviousWindow)\n\t\n\treturn requestsInCurrentWindow + requestsInPreviousWindow\n`;\n", "export const resetScript = `\n      local pattern = KEYS[1]\n\n      -- Initialize cursor to start from 0\n      local cursor = \"0\"\n\n      repeat\n          -- Scan for keys matching the pattern\n          local scan_result = redis.call('SCAN', cursor, 'MATCH', pattern)\n\n          -- Extract cursor for the next iteration\n          cursor = scan_result[1]\n\n          -- Extract keys from the scan result\n          local keys = scan_result[2]\n\n          for i=1, #keys do\n          redis.call('DEL', keys[i])\n          end\n\n      -- Continue scanning until cursor is 0 (end of keyspace)\n      until cursor == \"0\"\n    `;\n", "import * as Single from \"./single\"\nimport * as Multi from \"./multi\"\nimport { resetScript } from \"./reset\"\n\nexport type ScriptInfo = {\n  script: string,\n  hash: string\n}\n\ntype Algorithm = {\n  limit: ScriptInfo,\n  getRemaining: ScriptInfo,\n}\n\ntype AlgorithmKind = \n  | \"fixedWindow\"\n  | \"slidingWindow\"\n  | \"tokenBucket\"\n  | \"cachedFixedWindow\"\n\nexport const SCRIPTS: {\n  singleRegion: Record<AlgorithmKind, Algorithm>,\n  multiRegion: Record<Exclude<AlgorithmKind, \"tokenBucket\" | \"cachedFixedWindow\">, Algorithm>,\n} = {\n  singleRegion: {\n    fixedWindow: {\n      limit: {\n        script: Single.fixedWindowLimitScript,\n        hash: \"b13943e359636db027ad280f1def143f02158c13\"\n      },\n      getRemaining: {\n        script: Single.fixedWindowRemainingTokensScript,\n        hash: \"8c4c341934502aee132643ffbe58ead3450e5208\"\n      },\n    },\n    slidingWindow: {\n      limit: {\n        script: Single.slidingWindowLimitScript,\n        hash: \"e1391e429b699c780eb0480350cd5b7280fd9213\"\n      },\n      getRemaining: {\n        script: Single.slidingWindowRemainingTokensScript,\n        hash: \"65a73ac5a05bf9712903bc304b77268980c1c417\"\n      },\n    },\n    tokenBucket: {\n      limit: {\n        script: Single.tokenBucketLimitScript,\n        hash: \"5bece90aeef8189a8cfd28995b479529e270b3c6\"\n      },\n      getRemaining: {\n        script: Single.tokenBucketRemainingTokensScript,\n        hash: \"a15be2bb1db2a15f7c82db06146f9d08983900d0\"\n      },\n    },\n    cachedFixedWindow: {\n      limit: {\n        script: Single.cachedFixedWindowLimitScript,\n        hash: \"c26b12703dd137939b9a69a3a9b18e906a2d940f\"\n      },\n      getRemaining: {\n        script: Single.cachedFixedWindowRemainingTokenScript,\n        hash: \"8e8f222ccae68b595ee6e3f3bf2199629a62b91a\"\n      },\n    }\n  },\n  multiRegion: {\n    fixedWindow: {\n      limit: {\n        script: Multi.fixedWindowLimitScript,\n        hash: \"a8c14f3835aa87bd70e5e2116081b81664abcf5c\"\n      },\n      getRemaining: {\n        script: Multi.fixedWindowRemainingTokensScript,\n        hash: \"8ab8322d0ed5fe5ac8eb08f0c2e4557f1b4816fd\"\n      },\n    },\n    slidingWindow: {\n      limit: {\n        script: Multi.slidingWindowLimitScript,\n        hash: \"cb4fdc2575056df7c6d422764df0de3a08d6753b\"\n      },\n      getRemaining: {\n        script: Multi.slidingWindowRemainingTokensScript,\n        hash: \"558c9306b7ec54abb50747fe0b17e5d44bd24868\"\n      },\n    },\n  }\n}\n\n/** COMMON */\nexport const RESET_SCRIPT: ScriptInfo = {\n  script: resetScript,\n  hash: \"54bd274ddc59fb3be0f42deee2f64322a10e2b50\"\n}", "import type { Redis as RedisCore } from \"@upstash/redis\";\nimport type { <PERSON>eo } from \"./analytics\";\n\n/**\n * EphemeralCache is used to block certain identifiers right away in case they have already exceeded the ratelimit.\n */\nexport type EphemeralCache = {\n  isBlocked: (identifier: string) => { blocked: boolean; reset: number };\n  blockUntil: (identifier: string, reset: number) => void;\n\n  set: (key: string, value: number) => void;\n  get: (key: string) => number | null;\n\n  incr: (key: string) => number;\n\n  pop: (key: string) => void;\n  empty: () => void;\n\n  size: () => number;\n}\n\nexport type RegionContext = {\n  redis: Redis;\n  cache?: EphemeralCache,\n};\nexport type MultiRegionContext = { regionContexts: Omit<RegionContext[], \"cache\">; cache?: EphemeralCache };\n\nexport type RatelimitResponseType = \"timeout\" | \"cacheBlock\" | \"denyList\"\n\nexport type Context = RegionContext | MultiRegionContext;\nexport type RatelimitResponse = {\n  /**\n   * Whether the request may pass(true) or exceeded the limit(false)\n   */\n  success: boolean;\n  /**\n   * Maximum number of requests allowed within a window.\n   */\n  limit: number;\n  /**\n   * How many requests the user has left within the current window.\n   */\n  remaining: number;\n  /**\n   * Unix timestamp in milliseconds when the limits are reset.\n   */\n  reset: number;\n\n  /**\n   * For the MultiRegion setup we do some synchronizing in the background, after returning the current limit.\n   * Or when analytics is enabled, we send the analytics asynchronously after returning the limit.\n   * In most case you can simply ignore this.\n   *\n   * On Vercel Edge or Cloudflare workers, you need to explicitly handle the pending Promise like this:\n   *\n   * ```ts\n   * const { pending } = await ratelimit.limit(\"id\")\n   * context.waitUntil(pending)\n   * ```\n   *\n   * See `waitUntil` documentation in\n   * [Cloudflare](https://developers.cloudflare.com/workers/runtime-apis/handlers/fetch/#contextwaituntil)\n   * and [Vercel](https://vercel.com/docs/functions/edge-middleware/middleware-api#waituntil)\n   * for more details.\n   * ```\n   */\n  pending: Promise<unknown>;\n\n  /**\n   * Reason behind the result in `success` field.\n   * - Is set to \"timeout\" when request times out\n   * - Is set to \"cacheBlock\" when an identifier is blocked through cache without calling redis because it was\n   *    rate limited previously.\n   * - Is set to \"denyList\" when identifier or one of ip/user-agent/country parameters is in deny list. To enable\n   *    deny list, see `enableProtection` parameter. To edit the deny list, see the Upstash Ratelimit Dashboard\n   *    at https://console.upstash.com/ratelimit.\n   * - Is set to undefined if rate limit check had to use Redis. This happens in cases when `success` field in\n   *    the response is true. It can also happen the first time sucecss is false.\n   */\n  reason?: RatelimitResponseType;\n\n  /**\n   * The value which was in the deny list if reason: \"denyList\"\n   */\n  deniedValue?: DeniedValue\n};\n\nexport type Algorithm<TContext> = () => {\n  limit: (\n    ctx: TContext,\n    identifier: string,\n    rate?: number,\n    opts?: {\n      cache?: EphemeralCache;\n    },\n  ) => Promise<RatelimitResponse>;\n  getRemaining: (ctx: TContext, identifier: string) => Promise<{\n    remaining: number,\n    reset: number\n  }>;\n  resetTokens: (ctx: TContext, identifier: string) => Promise<void>;\n};\n\nexport type IsDenied = 0 | 1;\n\nexport type DeniedValue = string | undefined;\nexport type DenyListResponse = { deniedValue: DeniedValue, invalidIpDenyList: boolean }\n\nexport const DenyListExtension = \"denyList\" as const\nexport const IpDenyListKey = \"ipDenyList\" as const\nexport const IpDenyListStatusKey = \"ipDenyListStatus\" as const\n\nexport type LimitPayload = [RatelimitResponse, DenyListResponse];\nexport type LimitOptions = {\n  geo?: Geo,\n  rate?: number,\n  ip?: string,\n  userAgent?: string,\n  country?: string\n}\n\nexport type Redis = RedisCore\n", "export const checkDenyListScript = `\n  -- Checks if values provideed in ARGV are present in the deny lists.\n  -- This is done using the allDenyListsKey below.\n\n  -- Additionally, checks the status of the ip deny list using the\n  -- ipDenyListStatusKey below. Here are the possible states of the\n  -- ipDenyListStatusKey key:\n  -- * status == -1: set to \"disabled\" with no TTL\n  -- * status == -2: not set, meaning that is was set before but expired\n  -- * status  >  0: set to \"valid\", with a TTL\n  --\n  -- In the case of status == -2, we set the status to \"pending\" with\n  -- 30 second ttl. During this time, the process which got status == -2\n  -- will update the ip deny list.\n\n  local allDenyListsKey     = KEYS[1]\n  local ipDenyListStatusKey = KEYS[2]\n\n  local results = redis.call('SMISMEMBER', allDenyListsKey, unpack(ARGV))\n  local status  = redis.call('TTL', ipDenyListStatusKey)\n  if status == -2 then\n    redis.call('SETEX', ipDenyList<PERSON>tat<PERSON><PERSON><PERSON>, 30, \"pending\")\n  end\n\n  return { results, status }\n`", "import type { Redis } from \"../types\";\nimport { DenyListEx<PERSON>ion, IpDenyList<PERSON>ey, IpDenyListStatusKey } from \"../types\"\nimport { getIpListTTL } from \"./time\"\n\nconst baseUrl = \"https://raw.githubusercontent.com/stamparm/ipsum/master/levels\"\n\nexport class ThresholdError extends Error {\n  constructor(threshold: number) {\n    super(`Allowed threshold values are from 1 to 8, 1 and 8 included. Received: ${threshold}`);\n    this.name = \"ThresholdError\";\n  }\n}\n\n/**\n * Fetches the ips from the ipsum.txt at github\n * \n * In the repo we are using, 30+ ip lists are aggregated. The results are\n * stores in text files from 1 to 8.\n * https://github.com/stamparm/ipsum/tree/master/levels\n * \n * X.txt file holds ips which are in at least X of the lists.\n *\n * @param threshold ips with less than or equal to the threshold are not included\n * @returns list of ips\n */\nconst getIpDenyList = async (threshold: number) => {\n  if (typeof threshold !== \"number\" || threshold < 1 || threshold > 8) {\n    throw new ThresholdError(threshold)\n  }\n\n  try {\n    // Fetch data from the URL\n    const response = await fetch(`${baseUrl}/${threshold}.txt`)\n    if (!response.ok) {\n      throw new Error(`Error fetching data: ${response.statusText}`)\n    }\n    const data = await response.text()\n\n    // Process the data\n    const lines = data.split(\"\\n\")\n    return lines.filter((value) => value.length > 0) // remove empty values\n  } catch (error) {\n    throw new Error(`Failed to fetch ip deny list: ${error}`)\n  }\n}\n\n/**\n * Gets the list of ips from the github source which are not in the\n * deny list already\n * \n * First, gets the ip list from github using the threshold. Then, calls redis with\n * a transaction which does the following:\n * - subtract the current ip deny list from all\n * - delete current ip deny list\n * - recreate ip deny list with the ips from github. Ips already in the users own lists\n *   are excluded.\n * - status key is set to valid with ttl until next 2 AM UTC, which is a bit later than\n *   when the list is updated on github.\n *\n * @param redis redis instance\n * @param prefix ratelimit prefix\n * @param threshold ips with less than or equal to the threshold are not included\n * @param ttl time to live in milliseconds for the status flag. Optional. If not\n *  passed, ttl is infferred from current time.\n * @returns list of ips which are not in the deny list\n */\nexport const updateIpDenyList = async (\n  redis: Redis,\n  prefix: string,\n  threshold: number,\n  ttl?: number\n) => {\n  const allIps = await getIpDenyList(threshold)\n\n  const allDenyLists = [prefix, DenyListExtension, \"all\"].join(\":\")\n  const ipDenyList = [prefix, DenyListExtension, IpDenyListKey].join(\":\")\n  const statusKey = [prefix, IpDenyListStatusKey].join(\":\")\n\n  const transaction = redis.multi()\n\n  // remove the old ip deny list from the all set\n  transaction.sdiffstore(allDenyLists, allDenyLists, ipDenyList)\n\n  // delete the old ip deny list and create new one\n  transaction.del(ipDenyList)\n\n  transaction.sadd(ipDenyList, allIps.at(0), ...allIps.slice(1))\n\n  // make all deny list and ip deny list disjoint by removing duplicate\n  // ones from ip deny list\n  transaction.sdiffstore(ipDenyList, ipDenyList, allDenyLists)\n\n  // add remaining ips to all list\n  transaction.sunionstore(allDenyLists, allDenyLists, ipDenyList)\n\n  // set status key with ttl\n  transaction.set(statusKey, \"valid\", {px: ttl ?? getIpListTTL()})\n\n  return await transaction.exec()\n}\n\n/**\n * Disables the ip deny list by removing the ip deny list from the all\n * set and removing the ip deny list. Also sets the status key to disabled\n * with no ttl.\n * \n * @param redis redis instance\n * @param prefix ratelimit prefix\n * @returns \n */\nexport const disableIpDenyList = async (redis: Redis, prefix: string) => {\n  const allDenyListsKey = [prefix, DenyListExtension, \"all\"].join(\":\")\n  const ipDenyListKey = [prefix, DenyListExtension, IpDenyListKey].join(\":\")\n  const statusKey = [prefix, IpDenyListStatusKey].join(\":\")\n\n  const transaction = redis.multi()\n\n  // remove the old ip deny list from the all set\n  transaction.sdiffstore(allDenyListsKey, allDenyListsKey, ipDenyListKey)\n\n  // delete the old ip deny list\n  transaction.del(ipDenyListKey)\n\n  // set to disabled\n  // this way, the TTL command in checkDenyListScript will return -1.\n  transaction.set(statusKey, \"disabled\")\n\n  return await transaction.exec()\n}\n", "\n// Number of milliseconds in one hour\nconst MILLISECONDS_IN_HOUR = 60 * 60 * 1000;\n\n// Number of milliseconds in one day\nconst MILLISECONDS_IN_DAY = 24 * MILLISECONDS_IN_HOUR;\n\n// Number of milliseconds from the current time to 2 AM UTC\nconst MILLISECONDS_TO_2AM = 2 * MILLISECONDS_IN_HOUR;\n\nexport const getIpListTTL = (time?: number) => {\n  const now = time || Date.now();\n\n  // Time since the last 2 AM UTC\n  const timeSinceLast2AM = (now - MILLISECONDS_TO_2AM) % MILLISECONDS_IN_DAY;\n\n  // Remaining time until the next 2 AM UTC\n  return MILLISECONDS_IN_DAY - timeSinceLast2AM;\n}\n  ", "import type { DeniedValue, DenyListResponse, LimitPayload} from \"../types\";\nimport { DenyListExtension, IpDenyListStatusKey } from \"../types\"\nimport type { RatelimitResponse, Redis } from \"../types\"\nimport { Cache } from \"../cache\";\nimport { checkDenyListScript } from \"./scripts\";\nimport { updateIpDenyList } from \"./ip-deny-list\";\n\n\nconst denyListCache = new Cache(new Map());\n\n/**\n * Checks items in members list and returns the first denied member\n * in denyListCache if there are any.\n * \n * @param members list of values to check against the cache\n * @returns a member from the cache. If there is none, returns undefined\n */\nexport const checkDenyListCache = (members: string[]): DeniedValue => {\n  return members.find(\n    member => denyListCache.isBlocked(member).blocked\n  );\n}\n\n/**\n * Blocks a member for 1 minute.\n * \n * If there are more than 1000 elements in the cache, empties\n * it so that the cache doesn't grow in size indefinetely.\n * \n * @param member member to block\n */\nconst blockMember = (member: string) => {\n  if (denyListCache.size() > 1000) denyListCache.empty();\n  denyListCache.blockUntil(member, Date.now() + 60_000);\n}\n\n/**\n * Checks if identifier or any of the values are in any of\n * the denied lists in Redis.\n * \n * If some value is in a deny list, we block the identifier for a minute.\n * \n * @param redis redis client\n * @param prefix ratelimit prefix\n * @param members List of values (identifier, ip, user agent, country)\n * @returns true if a member is in deny list at Redis\n */\nexport const checkDenyList = async (\n  redis: Redis,\n  prefix: string,\n  members: string[]\n): Promise<DenyListResponse> => {\n  const [ deniedValues, ipDenyListStatus ] = await redis.eval(\n    checkDenyListScript,\n    [\n      [prefix, DenyListExtension, \"all\"].join(\":\"),\n      [prefix, IpDenyListStatusKey].join(\":\"),\n    ],\n    members\n  ) as [boolean[], number];\n\n  let deniedValue: DeniedValue = undefined;\n  deniedValues.map((memberDenied, index) => {\n    if (memberDenied) {\n      blockMember(members[index])\n      deniedValue = members[index]\n    }\n  })\n\n  return {\n    deniedValue,\n    invalidIpDenyList: ipDenyListStatus === -2\n  };\n};\n\n/**\n * Overrides the rate limit response if deny list\n * response indicates that value is in deny list.\n * \n * @param ratelimitResponse \n * @param denyListResponse \n * @returns \n */\nexport const resolveLimitPayload = (\n  redis: Redis,\n  prefix: string,\n  [ratelimitResponse, denyListResponse]: LimitPayload,\n  threshold: number\n): RatelimitResponse => {\n\n  if (denyListResponse.deniedValue) {\n    ratelimitResponse.success = false;\n    ratelimitResponse.remaining = 0;\n    ratelimitResponse.reason = \"denyList\";\n    ratelimitResponse.deniedValue = denyListResponse.deniedValue\n  }\n\n  if (denyListResponse.invalidIpDenyList) {\n    const updatePromise = updateIpDenyList(redis, prefix, threshold)\n    ratelimitResponse.pending = Promise.all([\n      ratelimitResponse.pending,\n      updatePromise\n    ])\n  }\n\n  return ratelimitResponse;\n};\n\n/**\n * \n * @returns Default response to return when some item\n *  is in deny list.\n */\nexport const defaultDeniedResponse = (deniedValue: string): RatelimitResponse => {\n  return {\n    success: false,\n    limit: 0,\n    remaining: 0,\n    reset: 0,\n    pending: Promise.resolve(),\n    reason: \"denyList\",\n    deniedValue: deniedValue\n  }\n}\n", "import { Analytics } from \"./analytics\";\nimport { C<PERSON> } from \"./cache\";\nimport type { Algorithm, Context, LimitOptions, LimitPayload, RatelimitResponse, Redis } from \"./types\";\nimport { checkDenyList, checkDenyListCache, defaultDeniedResponse, resolveLimitPayload } from \"./deny-list/index\";\n\nexport class TimeoutError extends Error {\n  constructor() {\n    super(\"Timeout\");\n    this.name = \"TimeoutError\";\n  }\n}\nexport type RatelimitConfig<TContext> = {\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - Ratelimiter.fixedWindow\n   * - Ratelimiter.slidingWindow\n   * - Ratelimiter.tokenBucket\n   */\n\n  limiter: Algorithm<TContext>;\n\n  ctx: TContext;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the  ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   *\n   * @default 5000\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * Enables deny list. If set to true, requests with identifier or ip/user-agent/countrie\n   * in the deny list will be rejected automatically. To edit the deny list, check out the\n   * ratelimit dashboard at https://console.upstash.com/ratelimit\n   * \n   * @default false\n   */\n  enableProtection?: boolean\n\n  denyListThreshold?: number\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new Ratelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: Ratelimit.slidingWindow(\n *      10,     // Allow 10 requests per window of 30 minutes\n *      \"30 m\", // interval of 30 minutes\n *    ),\n * })\n *\n * ```\n */\nexport abstract class Ratelimit<TContext extends Context> {\n  protected readonly limiter: Algorithm<TContext>;\n\n  protected readonly ctx: TContext;\n\n  protected readonly prefix: string;\n\n  protected readonly timeout: number;\n\n  protected readonly primaryRedis: Redis;\n\n  protected readonly analytics?: Analytics;\n\n  protected readonly enableProtection: boolean;\n\n  protected readonly denyListThreshold: number\n\n  constructor(config: RatelimitConfig<TContext>) {\n    this.ctx = config.ctx;\n    this.limiter = config.limiter;\n    this.timeout = config.timeout ?? 5000;\n    this.prefix = config.prefix ?? \"@upstash/ratelimit\";\n\n    this.enableProtection = config.enableProtection ?? false;\n    this.denyListThreshold = config.denyListThreshold ?? 6;\n\n    this.primaryRedis = (\"redis\" in this.ctx) ? this.ctx.redis : this.ctx.regionContexts[0].redis\n    this.analytics = config.analytics\n      ? new Analytics({\n        redis: this.primaryRedis,\n        prefix: this.prefix,\n      })\n      : undefined;\n\n    if (config.ephemeralCache instanceof Map) {\n      this.ctx.cache = new Cache(config.ephemeralCache);\n    } else if (config.ephemeralCache === undefined) {\n      this.ctx.cache = new Cache(new Map());\n    }\n  }\n\n  /**\n   * Determine if a request should pass or be rejected based on the identifier and previously chosen ratelimit.\n   *\n   * Use this if you want to reject all requests that you can not handle right now.\n   *\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(10, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.limit(id)\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   *\n   * @param req.rate - The rate at which tokens will be added or consumed from the token bucket. A higher rate allows for more requests to be processed. Defaults to 1 token per interval if not specified.\n   *\n   * Usage with `req.rate`\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(100, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.limit(id, {rate: 10})\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   */\n  public limit = async (\n    identifier: string,\n    req?: LimitOptions,\n  ): Promise<RatelimitResponse> => {\n\n    let timeoutId: any = null;\n    try {\n      const response = this.getRatelimitResponse(identifier, req);\n      const { responseArray, newTimeoutId } = this.applyTimeout(response);\n      timeoutId = newTimeoutId;\n\n      const timedResponse = await Promise.race(responseArray);\n      const finalResponse = this.submitAnalytics(timedResponse, identifier, req);\n      return finalResponse;\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    }\n  };\n\n  /**\n   * Block until the request may pass or timeout is reached.\n   *\n   * This method returns a promise that resolves as soon as the request may be processed\n   * or after the timeout has been reached.\n   *\n   * Use this if you want to delay the request until it is ready to get processed.\n   *\n   * @example\n   * ```ts\n   *  const ratelimit = new Ratelimit({\n   *    redis: Redis.fromEnv(),\n   *    limiter: Ratelimit.slidingWindow(10, \"10 s\")\n   *  })\n   *\n   *  const { success } = await ratelimit.blockUntilReady(id, 60_000)\n   *  if (!success){\n   *    return \"Nope\"\n   *  }\n   *  return \"Yes\"\n   * ```\n   */\n  public blockUntilReady = async (\n    /**\n     * An identifier per user or api.\n     * Choose a userID, or api token, or ip address.\n     *\n     * If you want to limit your api across all users, you can set a constant string.\n     */\n    identifier: string,\n    /**\n     * Maximum duration to wait in milliseconds.\n     * After this time the request will be denied.\n     */\n    timeout: number,\n  ): Promise<RatelimitResponse> => {\n    if (timeout <= 0) {\n      throw new Error(\"timeout must be positive\");\n    }\n    let res: RatelimitResponse;\n\n    const deadline = Date.now() + timeout;\n    while (true) {\n      res = await this.limit(identifier);\n      if (res.success) {\n        break;\n      }\n      if (res.reset === 0) {\n        throw new Error(\"This should not happen\");\n      }\n\n      const wait = Math.min(res.reset, deadline) - Date.now();\n      await new Promise((r) => setTimeout(r, wait));\n\n      if (Date.now() > deadline) {\n        break;\n      }\n    }\n    return res!;\n  };\n\n  public resetUsedTokens = async (identifier: string) => {\n    const pattern = [this.prefix, identifier].join(\":\");\n    await this.limiter().resetTokens(this.ctx, pattern);\n  };\n\n  /**\n   * Returns the remaining token count together with a reset timestamps\n   * \n   * @param identifier identifir to check\n   * @returns object with `remaining` and reset fields. `remaining` denotes\n   *          the remaining tokens and reset denotes the timestamp when the\n   *          tokens reset.\n   */\n  public getRemaining = async (identifier: string): Promise<{\n    remaining: number;\n    reset: number;\n  }> => {\n    const pattern = [this.prefix, identifier].join(\":\");\n\n    return await this.limiter().getRemaining(this.ctx, pattern);\n  };\n\n  /**\n   * Checks if the identifier or the values in req are in the deny list cache.\n   * If so, returns the default denied response.\n   * \n   * Otherwise, calls redis to check the rate limit and deny list. Returns after\n   * resolving the result. Resolving is overriding the rate limit result if\n   * the some value is in deny list.\n   * \n   * @param identifier identifier to block\n   * @param req options with ip, user agent, country, rate and geo info\n   * @returns rate limit response\n   */\n  private getRatelimitResponse = async (\n    identifier: string,\n    req?: LimitOptions\n  ): Promise<RatelimitResponse> => {\n    const key = this.getKey(identifier);\n    const definedMembers = this.getDefinedMembers(identifier, req);\n\n    const deniedValue = checkDenyListCache(definedMembers)\n\n    const result: LimitPayload = deniedValue ? [defaultDeniedResponse(deniedValue), { deniedValue, invalidIpDenyList: false }] : (await Promise.all([\n      this.limiter().limit(this.ctx, key, req?.rate),\n      this.enableProtection\n        ? checkDenyList(this.primaryRedis, this.prefix, definedMembers)\n        : { deniedValue: undefined, invalidIpDenyList: false }\n    ]));\n\n    return resolveLimitPayload(this.primaryRedis, this.prefix, result, this.denyListThreshold)\n  };\n\n  /**\n   * Creates an array with the original response promise and a timeout promise\n   * if this.timeout > 0.\n   * \n   * @param response Ratelimit response promise\n   * @returns array with the response and timeout promise. also includes the timeout id\n   */\n  private applyTimeout = (response: Promise<RatelimitResponse>) => {\n    let newTimeoutId: any = null;\n    const responseArray: Array<Promise<RatelimitResponse>> = [response];\n\n    if (this.timeout > 0) {\n      const timeoutResponse = new Promise<RatelimitResponse>((resolve) => {\n        newTimeoutId = setTimeout(() => {\n          resolve({\n            success: true,\n            limit: 0,\n            remaining: 0,\n            reset: 0,\n            pending: Promise.resolve(),\n            reason: \"timeout\"\n          });\n        }, this.timeout);\n      })\n      responseArray.push(timeoutResponse);\n    }\n\n    return {\n      responseArray,\n      newTimeoutId,\n    }\n  }\n\n  /**\n   * submits analytics if this.analytics is set\n   * \n   * @param ratelimitResponse final rate limit response\n   * @param identifier identifier to submit\n   * @param req limit options\n   * @returns rate limit response after updating the .pending field\n   */\n  private submitAnalytics = (\n    ratelimitResponse: RatelimitResponse,\n    identifier: string,\n    req?: Pick<LimitOptions, \"geo\">,\n  ) => {\n    if (this.analytics) {\n      try {\n        const geo = req ? this.analytics.extractGeo(req) : undefined;\n        const analyticsP = this.analytics\n          .record({\n            identifier: ratelimitResponse.reason === \"denyList\" // if in denyList, use denied value as identifier\n              ? ratelimitResponse.deniedValue!\n              : identifier,\n            time: Date.now(),\n            success: ratelimitResponse.reason === \"denyList\" // if in denyList, label success as \"denied\"\n              ? \"denied\"\n              : ratelimitResponse.success,\n            ...geo,\n          })\n          .catch((error) => {\n            let errorMessage = \"Failed to record analytics\"\n            if (`${error}`.includes(\"WRONGTYPE\")) {\n              errorMessage = `\n    Failed to record analytics. See the information below:\n\n    This can occur when you uprade to Ratelimit version 1.1.2\n    or later from an earlier version.\n\n    This occurs simply because the way we store analytics data\n    has changed. To avoid getting this error, disable analytics\n    for *an hour*, then simply enable it back.\\n\n    `\n            }\n            console.warn(errorMessage, error);\n          });\n        ratelimitResponse.pending = Promise.all([ratelimitResponse.pending, analyticsP]);\n      } catch (error) {\n        console.warn(\"Failed to record analytics\", error);\n      };\n    };\n    return ratelimitResponse;\n  }\n\n  private getKey = (identifier: string): string => {\n    return [this.prefix, identifier].join(\":\");\n  }\n\n  /**\n   * returns a list of defined values from\n   * [identifier, req.ip, req.userAgent, req.country]\n   * \n   * @param identifier identifier\n   * @param req limit options\n   * @returns list of defined values\n   */\n  private getDefinedMembers = (\n    identifier: string,\n    req?: Pick<LimitOptions, \"ip\" | \"userAgent\" | \"country\">\n  ): string[] => {\n    const members = [identifier, req?.ip, req?.userAgent, req?.country];\n    return (members as string[]).filter(Boolean);\n  }\n}\n", "import { Cache } from \"./cache\";\nimport type { Duration } from \"./duration\";\nimport { ms } from \"./duration\";\nimport { safeEval } from \"./hash\";\nimport { RESET_SCRIPT, SCRIPTS } from \"./lua-scripts/hash\";\n\n\nimport { Ratelimit } from \"./ratelimit\";\nimport type { Algorithm, MultiRegionContext } from \"./types\";\n\nimport type { Redis } from \"./types\";\n\nfunction randomId(): string {\n  let result = \"\";\n  const characters = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n  const charactersLength = characters.length;\n  for (let i = 0; i < 16; i++) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n  }\n  return result;\n}\n\nexport type MultiRegionRatelimitConfig = {\n  /**\n   * Instances of `@upstash/redis`\n   * @see https://github.com/upstash/upstash-redis#quick-start\n   */\n  redis: Redis[];\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - MultiRegionRatelimit.fixedWindow\n   */\n  limiter: Algorithm<MultiRegionContext>;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * If enabled, lua scripts will be sent to Redis with SCRIPT LOAD durint the first request.\n   * In the subsequent requests, hash of the script will be used to invoke it\n   * \n   * @default true\n   */\n  cacheScripts?: boolean;\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new MultiRegionRatelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: MultiRegionRatelimit.fixedWindow(\n *      10,     // Allow 10 requests per window of 30 minutes\n *      \"30 m\", // interval of 30 minutes\n *    )\n * })\n *\n * ```\n */\nexport class MultiRegionRatelimit extends Ratelimit<MultiRegionContext> {\n  /**\n   * Create a new Ratelimit instance by providing a `@upstash/redis` instance and the algorithn of your choice.\n   */\n  constructor(config: MultiRegionRatelimitConfig) {\n    super({\n      prefix: config.prefix,\n      limiter: config.limiter,\n      timeout: config.timeout,\n      analytics: config.analytics,\n      ctx: {\n        regionContexts: config.redis.map(redis => ({\n          redis: redis,\n        })),\n        cache: config.ephemeralCache ? new Cache(config.ephemeralCache) : undefined,\n      },\n    });\n  }\n\n  /**\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static fixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<MultiRegionContext> {\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: MultiRegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const requestId = randomId();\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const dbs: { redis: Redis; request: Promise<string[]> }[] = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.fixedWindow.limit,\n            [key],\n            [requestId, windowDuration, incrementBy],\n          ) as Promise<string[]>,\n        }));\n\n        // The firstResponse is an array of string at every EVEN indexes and rate at which the tokens are used at every ODD indexes\n        const firstResponse = await Promise.any(dbs.map((s) => s.request));\n\n        const usedTokens = firstResponse.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const remaining = tokens - usedTokens;\n\n        /**\n         * If the length between two databases does not match, we sync the two databases\n         */\n        async function sync() {\n          const individualIDs = await Promise.all(dbs.map((s) => s.request));\n\n          const allIDs = [...new Set(\n            individualIDs.flat()\n              .reduce((acc: string[], curr, index) => {\n                if (index % 2 === 0) {\n                  acc.push(curr);\n                }\n                return acc;\n              }, []),\n          ).values()];\n\n          for (const db of dbs) {\n            const usedDbTokensRequest = await db.request;\n            const usedDbTokens = usedDbTokensRequest.reduce(\n              (accTokens: number, usedToken, index) => {\n                let parsedToken = 0;\n                if (index % 2) {\n                  parsedToken = Number.parseInt(usedToken);\n                }\n\n                return accTokens + parsedToken;\n              },\n              0,\n            );\n\n            const dbIdsRequest = await db.request;\n            const dbIds = dbIdsRequest.reduce((ids: string[], currentId, index) => {\n              if (index % 2 === 0) {\n                ids.push(currentId);\n              }\n              return ids;\n            }, []);\n            /**\n             * If the bucket in this db is already full, it doesn't matter which ids it contains.\n             * So we do not have to sync.\n             */\n            if (usedDbTokens >= tokens) {\n              continue;\n            }\n            const diff = allIDs.filter((id) => !dbIds.includes(id));\n            /**\n             * Don't waste a request if there is nothing to send\n             */\n            if (diff.length === 0) {\n              continue;\n            }\n\n            for (const requestId of diff) {\n              await db.redis.hset(key, { [requestId]: incrementBy });\n            }\n          }\n        }\n\n        /**\n         * Do not await sync. This should not run in the critical path.\n         */\n\n        const success = remaining > 0;\n        const reset = (bucket + 1) * windowDuration;\n\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success,\n          limit: tokens,\n          remaining,\n          reset,\n          pending: sync(),\n        };\n      },\n      async getRemaining(ctx: MultiRegionContext, identifier: string) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const dbs: { redis: Redis; request: Promise<string[]> }[] = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.fixedWindow.getRemaining,\n            [key],\n            [null]\n          ) as Promise<string[]>,\n        }));\n\n        // The firstResponse is an array of string at every EVEN indexes and rate at which the tokens are used at every ODD indexes\n        const firstResponse = await Promise.any(dbs.map((s) => s.request));\n        const usedTokens = firstResponse.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: MultiRegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await Promise.all(ctx.regionContexts.map((regionContext) => {\n          safeEval(\n            regionContext,\n            RESET_SCRIPT,\n            [pattern],\n            [null]\n          );\n        }))\n      },\n    });\n  }\n\n  /**\n   * Combined approach of `slidingLogs` and `fixedWindow` with lower storage\n   * costs than `slidingLogs` and improved boundary behavior by calculating a\n   * weighted score between two windows.\n   *\n   * **Pro:**\n   *\n   * Good performance allows this to scale to very high loads.\n   *\n   * **Con:**\n   *\n   * Nothing major.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - The duration in which the user can max X requests.\n   */\n  static slidingWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<MultiRegionContext> {\n    const windowSize = ms(window);\n\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: MultiRegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const requestId = randomId();\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const dbs = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.slidingWindow.limit,\n            [currentKey, previousKey],\n            [tokens, now, windowDuration, requestId, incrementBy],\n            // lua seems to return `1` for true and `null` for false\n          ) as Promise<[string[], string[], 1 | null]>,\n        }));\n\n        const percentageInCurrent = (now % windowDuration) / windowDuration;\n        const [current, previous, success] = await Promise.any(dbs.map((s) => s.request));\n\n        // in the case of success, the new request is not included in the current array.\n        // add it manually\n        if (success) {\n          current.push(requestId, incrementBy.toString())\n        }\n\n        const previousUsedTokens = previous.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const currentUsedTokens = current.reduce((accTokens: number, usedToken, index) => {\n          let parsedToken = 0;\n          if (index % 2) {\n            parsedToken = Number.parseInt(usedToken);\n          }\n\n          return accTokens + parsedToken;\n        }, 0);\n\n        const previousPartialUsed = Math.ceil(previousUsedTokens * (1 - percentageInCurrent));\n\n        const usedTokens = previousPartialUsed + currentUsedTokens;\n\n        const remaining = tokens - usedTokens;\n\n        /**\n         * If a database differs from the consensus, we sync it\n         */\n        async function sync() {\n          const res = await Promise.all(dbs.map((s) => s.request));\n\n          const allCurrentIds = [...new Set(\n            res\n              .flatMap(([current]) => current)\n              .reduce((acc: string[], curr, index) => {\n                if (index % 2 === 0) {\n                  acc.push(curr);\n                }\n                return acc;\n              }, []),\n          ).values()];\n\n          for (const db of dbs) {\n            const [current, _previous, _success] = await db.request;\n            const dbIds = current.reduce((ids: string[], currentId, index) => {\n              if (index % 2 === 0) {\n                ids.push(currentId);\n              }\n              return ids;\n            }, []);\n\n            const usedDbTokens = current.reduce((accTokens: number, usedToken, index) => {\n              let parsedToken = 0;\n              if (index % 2) {\n                parsedToken = Number.parseInt(usedToken);\n              }\n\n              return accTokens + parsedToken;\n            }, 0);\n            /**\n             * If the bucket in this db is already full, it doesn't matter which ids it contains.\n             * So we do not have to sync.\n             */\n            if (usedDbTokens >= tokens) {\n              continue;\n            }\n            const diff = allCurrentIds.filter((id) => !dbIds.includes(id));\n            /**\n             * Don't waste a request if there is nothing to send\n             */\n            if (diff.length === 0) {\n              continue;\n            }\n\n            for (const requestId of diff) {\n              await db.redis.hset(currentKey, { [requestId]: incrementBy });\n            }\n          }\n        }\n\n        // const success = remaining >= 0;\n        const reset = (currentWindow + 1) * windowDuration;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success: Boolean(success),\n          limit: tokens,\n          remaining: Math.max(0, remaining),\n          reset,\n          pending: sync(),\n        };\n      },\n      async getRemaining(ctx: MultiRegionContext, identifier: string) {\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        const dbs = ctx.regionContexts.map((regionContext) => ({\n          redis: regionContext.redis,\n          request: safeEval(\n            regionContext,\n            SCRIPTS.multiRegion.slidingWindow.getRemaining,\n            [currentKey, previousKey],\n            [now, windowSize],\n            // lua seems to return `1` for true and `null` for false\n          ) as Promise<number>,\n        }));\n\n        const usedTokens = await Promise.any(dbs.map((s) => s.request));\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (currentWindow + 1) * windowSize\n        };\n      },\n      async resetTokens(ctx: MultiRegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n\n        await Promise.all(ctx.regionContexts.map((regionContext) => {\n          safeEval(\n            regionContext,\n            RESET_SCRIPT,\n            [pattern],\n            [null]\n          );\n        }))\n      },\n    });\n  }\n}\n", "import type { Duration } from \"./duration\";\nimport { ms } from \"./duration\";\nimport { safeEval } from \"./hash\";\nimport { RESET_SCRIPT, SCRIPTS } from \"./lua-scripts/hash\";\nimport { tokenBucketIdentifierNotFound } from \"./lua-scripts/single\";\n\nimport { Ratelimit } from \"./ratelimit\";\nimport type { Algorithm, RegionContext } from \"./types\";\nimport type { Redis as RedisCore } from \"./types\";\n\n// Fix for https://github.com/upstash/ratelimit-js/issues/125\ntype Redis = Pick<RedisCore, \"get\" | \"set\">\n\nexport type RegionRatelimitConfig = {\n  /**\n   * Instance of `@upstash/redis`\n   * @see https://github.com/upstash/upstash-redis#quick-start\n   */\n  redis: Redis;\n  /**\n   * The ratelimiter function to use.\n   *\n   * Choose one of the predefined ones or implement your own.\n   * Available algorithms are exposed via static methods:\n   * - Ratelimiter.fixedWindow\n   * - Ratelimiter.slidingWindow\n   * - Ratelimiter.tokenBucket\n   */\n  limiter: Algorithm<RegionContext>;\n  /**\n   * All keys in redis are prefixed with this.\n   *\n   * @default `@upstash/ratelimit`\n   */\n  prefix?: string;\n\n  /**\n   * If enabled, the ratelimiter will keep a global cache of identifiers, that have\n   * exhausted their ratelimit. In serverless environments this is only possible if\n   * you create the ratelimiter instance outside of your handler function. While the\n   * function is still hot, the ratelimiter can block requests without having to\n   * request data from redis, thus saving time and money.\n   *\n   * Whenever an identifier has exceeded its limit, the ratelimiter will add it to an\n   * internal list together with its reset timestamp. If the same identifier makes a\n   * new request before it is reset, we can immediately reject it.\n   *\n   * Set to `false` to disable.\n   *\n   * If left undefined, a map is created automatically, but it can only work\n   * if the map or the ratelimit instance is created outside your serverless function handler.\n   */\n  ephemeralCache?: Map<string, number> | false;\n\n  /**\n   * If set, the ratelimiter will allow requests to pass after this many milliseconds.\n   *\n   * Use this if you want to allow requests in case of network problems\n   */\n  timeout?: number;\n\n  /**\n   * If enabled, the ratelimiter will store analytics data in redis, which you can check out at\n   * https://console.upstash.com/ratelimit\n   *\n   * @default false\n   */\n  analytics?: boolean;\n\n  /**\n   * @deprecated Has no affect since v2.0.3. Instead, hash values of scripts are\n   * hardcoded in the sdk and it attempts to run the script using EVALSHA (with the hash).\n   * If it fails, runs script load.\n   * \n   * Previously, if enabled, lua scripts were sent to Redis with SCRIPT LOAD durint the first request.\n   * In the subsequent requests, hash of the script would be used to invoke the scripts\n   * \n   * @default true\n   */\n  cacheScripts?: boolean;\n\n  /**\n   * @default false\n   */\n  enableProtection?: boolean\n\n  /**\n   * @default 6\n   */\n  denyListThreshold?: number\n};\n\n/**\n * Ratelimiter using serverless redis from https://upstash.com/\n *\n * @example\n * ```ts\n * const { limit } = new Ratelimit({\n *    redis: Redis.fromEnv(),\n *    limiter: Ratelimit.slidingWindow(\n *      \"30 m\", // interval of 30 minutes\n *      10,     // Allow 10 requests per window of 30 minutes\n *    )\n * })\n *\n * ```\n */\nexport class RegionRatelimit extends Ratelimit<RegionContext> {\n  /**\n   * Create a new Ratelimit instance by providing a `@upstash/redis` instance and the algorithm of your choice.\n   */\n\n  constructor(config: RegionRatelimitConfig) {\n    super({\n      prefix: config.prefix,\n      limiter: config.limiter,\n      timeout: config.timeout,\n      analytics: config.analytics,\n      ctx: {\n        redis: config.redis as RedisCore,\n      },\n      ephemeralCache: config.ephemeralCache,\n      enableProtection: config.enableProtection,\n      denyListThreshold: config.denyListThreshold\n    });\n  }\n\n  /**\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static fixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowDuration = ms(window);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const usedTokensAfterUpdate = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.fixedWindow.limit,\n          [key],\n          [windowDuration, incrementBy],\n        ) as number;\n\n        const success = usedTokensAfterUpdate <= tokens;\n\n        const remainingTokens = Math.max(0, tokens - usedTokensAfterUpdate);\n\n        const reset = (bucket + 1) * windowDuration;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n\n        return {\n          success,\n          limit: tokens,\n          remaining: remainingTokens,\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.fixedWindow.getRemaining,\n          [key],\n          [null],\n        ) as number;\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n\n  /**\n   * Combined approach of `slidingLogs` and `fixedWindow` with lower storage\n   * costs than `slidingLogs` and improved boundary behavior by calculating a\n   * weighted score between two windows.\n   *\n   * **Pro:**\n   *\n   * Good performance allows this to scale to very high loads.\n   *\n   * **Con:**\n   *\n   * Nothing major.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - The duration in which the user can max X requests.\n   */\n  static slidingWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowSize = ms(window);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        const now = Date.now();\n\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: tokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const remainingTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.slidingWindow.limit,\n          [currentKey, previousKey],\n          [tokens, now, windowSize, incrementBy],\n        ) as number;\n\n        const success = remainingTokens >= 0;\n\n        const reset = (currentWindow + 1) * windowSize;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n        return {\n          success,\n          limit: tokens,\n          remaining: Math.max(0, remainingTokens),\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        const now = Date.now();\n        const currentWindow = Math.floor(now / windowSize);\n        const currentKey = [identifier, currentWindow].join(\":\");\n        const previousWindow = currentWindow - 1;\n        const previousKey = [identifier, previousWindow].join(\":\");\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.slidingWindow.getRemaining,\n          [currentKey, previousKey],\n          [now, windowSize],\n        ) as number;\n\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (currentWindow + 1) * windowSize\n        }\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = [identifier, \"*\"].join(\":\");\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n\n  /**\n   * You have a bucket filled with `{maxTokens}` tokens that refills constantly\n   * at `{refillRate}` per `{interval}`.\n   * Every request will remove one token from the bucket and if there is no\n   * token to take, the request is rejected.\n   *\n   * **Pro:**\n   *\n   * - Bursts of requests are smoothed out and you can process them at a constant\n   * rate.\n   * - Allows to set a higher initial burst limit by setting `maxTokens` higher\n   * than `refillRate`\n   */\n  static tokenBucket(\n    /**\n     * How many tokens are refilled per `interval`\n     *\n     * An interval of `10s` and refillRate of 5 will cause a new token to be added every 2 seconds.\n     */\n    refillRate: number,\n    /**\n     * The interval for the `refillRate`\n     */\n    interval: Duration,\n    /**\n     * Maximum number of tokens.\n     * A newly created bucket starts with this many tokens.\n     * Useful to allow higher burst limits.\n     */\n    maxTokens: number,\n  ): Algorithm<RegionContext> {\n    const intervalDuration = ms(interval);\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        if (ctx.cache) {\n          const { blocked, reset } = ctx.cache.isBlocked(identifier);\n          if (blocked) {\n            return {\n              success: false,\n              limit: maxTokens,\n              remaining: 0,\n              reset: reset,\n              pending: Promise.resolve(),\n              reason: \"cacheBlock\"\n            };\n          }\n        }\n\n        const now = Date.now();\n\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const [remaining, reset] = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.tokenBucket.limit,\n          [identifier],\n          [maxTokens, intervalDuration, refillRate, now, incrementBy],\n        ) as [number, number];\n\n        const success = remaining >= 0;\n        if (ctx.cache && !success) {\n          ctx.cache.blockUntil(identifier, reset);\n        }\n\n        return {\n          success,\n          limit: maxTokens,\n          remaining,\n          reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n\n        const [remainingTokens, refilledAt] = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.tokenBucket.getRemaining,\n          [identifier],\n          [maxTokens],\n        ) as [number, number];\n\n        const freshRefillAt = Date.now() + intervalDuration\n        const identifierRefillsAt = refilledAt + intervalDuration\n\n        return {\n          remaining: remainingTokens,\n          reset: refilledAt === tokenBucketIdentifierNotFound ? freshRefillAt : identifierRefillsAt\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        const pattern = identifier;\n        if (ctx.cache) {\n          ctx.cache.pop(identifier)\n        }\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n  /**\n   * cachedFixedWindow first uses the local cache to decide if a request may pass and then updates\n   * it asynchronously.\n   * This is experimental and not yet recommended for production use.\n   *\n   * @experimental\n   *\n   * Each request inside a fixed time increases a counter.\n   * Once the counter reaches the maximum allowed number, all further requests are\n   * rejected.\n   *\n   * **Pro:**\n   *\n   * - Newer requests are not starved by old ones.\n   * - Low storage cost.\n   *\n   * **Con:**\n   *\n   * A burst of requests near the boundary of a window can result in a very\n   * high request rate because two windows will be filled with requests quickly.\n   *\n   * @param tokens - How many requests a user can make in each time window.\n   * @param window - A fixed timeframe\n   */\n  static cachedFixedWindow(\n    /**\n     * How many requests are allowed per window.\n     */\n    tokens: number,\n    /**\n     * The duration in which `tokens` requests are allowed.\n     */\n    window: Duration,\n  ): Algorithm<RegionContext> {\n    const windowDuration = ms(window);\n\n    return () => ({\n      async limit(ctx: RegionContext, identifier: string, rate?: number) {\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        const reset = (bucket + 1) * windowDuration;\n        const incrementBy = rate ? Math.max(1, rate) : 1;\n\n        const hit = typeof ctx.cache.get(key) === \"number\";\n        if (hit) {\n          const cachedTokensAfterUpdate = ctx.cache.incr(key);\n          const success = cachedTokensAfterUpdate < tokens;\n\n          const pending = success\n            ? safeEval(\n              ctx,\n              SCRIPTS.singleRegion.cachedFixedWindow.limit,\n              [key],\n              [windowDuration, incrementBy]\n            )\n            : Promise.resolve();\n\n          return {\n            success,\n            limit: tokens,\n            remaining: tokens - cachedTokensAfterUpdate,\n            reset: reset,\n            pending,\n          };\n        }\n\n        const usedTokensAfterUpdate = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.cachedFixedWindow.limit,\n          [key],\n          [windowDuration, incrementBy]\n        ) as number;\n        ctx.cache.set(key, usedTokensAfterUpdate);\n        const remaining = tokens - usedTokensAfterUpdate;\n\n        return {\n          success: remaining >= 0,\n          limit: tokens,\n          remaining,\n          reset: reset,\n          pending: Promise.resolve(),\n        };\n      },\n      async getRemaining(ctx: RegionContext, identifier: string) {\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n\n        const hit = typeof ctx.cache.get(key) === \"number\";\n        if (hit) {\n          const cachedUsedTokens = ctx.cache.get(key) ?? 0;\n          return {\n            remaining: Math.max(0, tokens - cachedUsedTokens),\n            reset: (bucket + 1) * windowDuration\n          };\n        }\n\n        const usedTokens = await safeEval(\n          ctx,\n          SCRIPTS.singleRegion.cachedFixedWindow.getRemaining,\n          [key],\n          [null],\n        ) as number;\n        return {\n          remaining: Math.max(0, tokens - usedTokens),\n          reset: (bucket + 1) * windowDuration\n        };\n      },\n      async resetTokens(ctx: RegionContext, identifier: string) {\n        // Empty the cache\n        if (!ctx.cache) {\n          throw new Error(\"This algorithm requires a cache\");\n        }\n\n        const bucket = Math.floor(Date.now() / windowDuration);\n        const key = [identifier, bucket].join(\":\");\n        ctx.cache.pop(key)\n\n        const pattern = [identifier, \"*\"].join(\":\");\n\n        await safeEval(\n          ctx,\n          RESET_SCRIPT,\n          [pattern],\n          [null],\n        ) as number;\n      },\n    });\n  }\n}\n"], "mappings": ";;;;;;;AACA,SAAS,aAAa,qBAAqB;AA+BpC,IAAM,YAAN,MAAgB;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EAEzB,YAAY,QAAyB;AACnC,SAAK,YAAY,IAAI,cAAc;AAAA;AAAA,MAEjC,OAAO,OAAO;AAAA,MACd,QAAQ;AAAA,MACR,QAAQ,OAAO,UAAU;AAAA,MACzB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,WAAW,KAAmC;AACnD,QAAI,IAAI,QAAQ,QAAW;AACzB,aAAO,IAAI;AAAA,IACb;AACA,QAAI,IAAI,OAAO,QAAW;AACxB,aAAO,IAAI;AAAA,IACb;AAEA,WAAO,CAAC;AAAA,EACV;AAAA,EAEA,MAAa,OAAO,OAA6B;AAC/C,UAAM,KAAK,UAAU,OAAO,KAAK,OAAO,KAAK;AAAA,EAC/C;AAAA,EAEA,MAAa,OACX,QACA,QACsB;AACtB,UAAM,iBAAiB,KAAK;AAAA,OAExB,KAAK,UAAU,UAAU,KAAK,IAAI,CAAC,IACjC,KAAK,UAAU,UAAU,MAAM,MAC9B,KAAK,KAAK;AAAA,MACf;AAAA,IACF;AACA,WAAO,KAAK,UAAU,6BAA6B,KAAK,OAAO,QAAQ,cAAc;AAAA,EACvF;AAAA,EAEA,MAAa,SAAS,SAAS,GAAkE;AAE/F,UAAM,iBAAiB,KAAK;AAAA,OAExB,KAAK,UAAU,UAAU,KAAK,IAAI,CAAC,IACjC,KAAK,UAAU,UAAU,MAAM,MAC9B,KAAK,KAAK;AAAA,MACf;AAAA,IACF;AACA,UAAM,UAAU,MAAM,KAAK,UAAU,kBAAkB,KAAK,OAAO,cAAc;AACjF,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,iBACX,gBAAwB,SACF;AACtB,UAAM,SAAS,MAAM,KAAK,UAAU,6BAA6B,KAAK,OAAO,SAAS,cAAc;AACpG,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,sBAAsB,gBAAwB,QAAiB,aAAsB;AAChG,aAAS,UAAU;AACnB,UAAM,YAAY;AAClB,WAAO,KAAK,UAAU,sBAAsB,KAAK,OAAO,gBAAgB,QAAQ,WAAW,WAAW;AAAA,EACxG;AACF;;;ACzGO,IAAM,QAAN,MAAsC;AAAA;AAAA;AAAA;AAAA,EAI1B;AAAA,EAEjB,YAAY,OAA4B;AACtC,SAAK,QAAQ;AAAA,EACf;AAAA,EAEO,UAAU,YAAyD;AACxE,QAAI,CAAC,KAAK,MAAM,IAAI,UAAU,GAAG;AAC/B,aAAO,EAAE,SAAS,OAAO,OAAO,EAAE;AAAA,IACpC;AACA,UAAM,QAAQ,KAAK,MAAM,IAAI,UAAU;AACvC,QAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,WAAK,MAAM,OAAO,UAAU;AAC5B,aAAO,EAAE,SAAS,OAAO,OAAO,EAAE;AAAA,IACpC;AAEA,WAAO,EAAE,SAAS,MAAM,MAAa;AAAA,EACvC;AAAA,EAEO,WAAW,YAAoB,OAAqB;AACzD,SAAK,MAAM,IAAI,YAAY,KAAK;AAAA,EAClC;AAAA,EAEO,IAAI,KAAa,OAAqB;AAC3C,SAAK,MAAM,IAAI,KAAK,KAAK;AAAA,EAC3B;AAAA,EACO,IAAI,KAA4B;AACrC,WAAO,KAAK,MAAM,IAAI,GAAG,KAAK;AAAA,EAChC;AAAA,EAEO,KAAK,KAAqB;AAC/B,QAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK;AACnC,aAAS;AACT,SAAK,MAAM,IAAI,KAAK,KAAK;AACzB,WAAO;AAAA,EACT;AAAA,EAEO,IAAI,KAAmB;AAC5B,SAAK,MAAM,OAAO,GAAG;AAAA,EACvB;AAAA,EAEO,QAAc;AACnB,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EAEO,OAAe;AACpB,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;;;AChDO,SAAS,GAAG,GAAqB;AACtC,QAAM,QAAQ,EAAE,MAAM,wBAAwB;AAC9C,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,gCAAgC,CAAC,EAAE;AAAA,EACrD;AACA,QAAM,OAAO,OAAO,SAAS,MAAM,CAAC,CAAC;AACrC,QAAM,OAAO,MAAM,CAAC;AAEpB,UAAQ,MAAM;AAAA,IACZ,KAAK,MAAM;AACT,aAAO;AAAA,IACT;AAAA,IACA,KAAK,KAAK;AACR,aAAO,OAAO;AAAA,IAChB;AAAA,IACA,KAAK,KAAK;AACR,aAAO,OAAO,MAAO;AAAA,IACvB;AAAA,IACA,KAAK,KAAK;AACR,aAAO,OAAO,MAAO,KAAK;AAAA,IAC5B;AAAA,IACA,KAAK,KAAK;AACR,aAAO,OAAO,MAAO,KAAK,KAAK;AAAA,IACjC;AAAA,IAEA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,CAAC,EAAE;AAAA,IACrD;AAAA,EACF;AACF;;;ACrBO,IAAM,WAAW,OACtB,KACA,QACA,MACA,SACG;AACH,MAAI;AACF,WAAO,MAAM,IAAI,MAAM,QAAQ,OAAO,MAAM,MAAM,IAAI;AAAA,EACxD,SAAS,OAAO;AACd,QAAI,GAAG,KAAK,GAAG,SAAS,UAAU,GAAG;AACnC,YAAM,OAAO,MAAM,IAAI,MAAM,WAAW,OAAO,MAAM;AAErD,UAAI,SAAS,OAAO,MAAM;AACxB,gBAAQ;AAAA,UACN;AAAA,QAGF;AAAA,MACF;AAEA,aAAO,MAAM,IAAI,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,IACjD;AACA,UAAM;AAAA,EACR;AACF;;;ACtCO,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe/B,IAAM,mCAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWzC,IAAM,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCjC,IAAM,qCAAqC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB3C,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwC/B,IAAM,gCAAgC;AAEtC,IAAM,mCAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAOvB,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAM/C,IAAM,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAerC,IAAM,wCAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACxJ9C,IAAMA,0BAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB/B,IAAMC,oCAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASzC,IAAMC,4BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCjC,IAAMC,sCAAqC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC7D3C,IAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACoBpB,IAAM,UAGT;AAAA,EACF,cAAc;AAAA,IACZ,aAAa;AAAA,MACX,OAAO;AAAA,QACL,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,QACL,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,OAAO;AAAA,QACL,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB,OAAO;AAAA,QACL,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,MACX,OAAO;AAAA,QACL,QAAcC;AAAA,QACd,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAcC;AAAA,QACd,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,QACL,QAAcC;AAAA,QACd,MAAM;AAAA,MACR;AAAA,MACA,cAAc;AAAA,QACZ,QAAcC;AAAA,QACd,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAGO,IAAM,eAA2B;AAAA,EACtC,QAAQ;AAAA,EACR,MAAM;AACR;;;ACcO,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;;;AC9G5B,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAnC;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAM,uBAAuB,KAAK,KAAK;AAGvC,IAAM,sBAAsB,KAAK;AAGjC,IAAM,sBAAsB,IAAI;AAEzB,IAAM,eAAe,CAAC,SAAkB;AAC7C,QAAM,MAAM,QAAQ,KAAK,IAAI;AAG7B,QAAM,oBAAoB,MAAM,uBAAuB;AAGvD,SAAO,sBAAsB;AAC/B;;;ADdA,IAAM,UAAU;AAET,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACxC,YAAY,WAAmB;AAC7B,UAAM,yEAAyE,SAAS,EAAE;AAC1F,SAAK,OAAO;AAAA,EACd;AACF;AAcA,IAAM,gBAAgB,OAAO,cAAsB;AACjD,MAAI,OAAO,cAAc,YAAY,YAAY,KAAK,YAAY,GAAG;AACnE,UAAM,IAAI,eAAe,SAAS;AAAA,EACpC;AAEA,MAAI;AAEF,UAAM,WAAW,MAAM,MAAM,GAAG,OAAO,IAAI,SAAS,MAAM;AAC1D,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,wBAAwB,SAAS,UAAU,EAAE;AAAA,IAC/D;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AAGjC,UAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,WAAO,MAAM,OAAO,CAAC,UAAU,MAAM,SAAS,CAAC;AAAA,EACjD,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,iCAAiC,KAAK,EAAE;AAAA,EAC1D;AACF;AAsBO,IAAM,mBAAmB,OAC9B,OACA,QACA,WACA,QACG;AACH,QAAM,SAAS,MAAM,cAAc,SAAS;AAE5C,QAAM,eAAe,CAAC,QAAQ,mBAAmB,KAAK,EAAE,KAAK,GAAG;AAChE,QAAM,aAAa,CAAC,QAAQ,mBAAmB,aAAa,EAAE,KAAK,GAAG;AACtE,QAAM,YAAY,CAAC,QAAQ,mBAAmB,EAAE,KAAK,GAAG;AAExD,QAAM,cAAc,MAAM,MAAM;AAGhC,cAAY,WAAW,cAAc,cAAc,UAAU;AAG7D,cAAY,IAAI,UAAU;AAE1B,cAAY,KAAK,YAAY,OAAO,GAAG,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC;AAI7D,cAAY,WAAW,YAAY,YAAY,YAAY;AAG3D,cAAY,YAAY,cAAc,cAAc,UAAU;AAG9D,cAAY,IAAI,WAAW,SAAS,EAAC,IAAI,OAAO,aAAa,EAAC,CAAC;AAE/D,SAAO,MAAM,YAAY,KAAK;AAChC;AAWO,IAAM,oBAAoB,OAAO,OAAc,WAAmB;AACvE,QAAM,kBAAkB,CAAC,QAAQ,mBAAmB,KAAK,EAAE,KAAK,GAAG;AACnE,QAAM,gBAAgB,CAAC,QAAQ,mBAAmB,aAAa,EAAE,KAAK,GAAG;AACzE,QAAM,YAAY,CAAC,QAAQ,mBAAmB,EAAE,KAAK,GAAG;AAExD,QAAM,cAAc,MAAM,MAAM;AAGhC,cAAY,WAAW,iBAAiB,iBAAiB,aAAa;AAGtE,cAAY,IAAI,aAAa;AAI7B,cAAY,IAAI,WAAW,UAAU;AAErC,SAAO,MAAM,YAAY,KAAK;AAChC;;;AExHA,IAAM,gBAAgB,IAAI,MAAM,oBAAI,IAAI,CAAC;AASlC,IAAM,qBAAqB,CAAC,YAAmC;AACpE,SAAO,QAAQ;AAAA,IACb,YAAU,cAAc,UAAU,MAAM,EAAE;AAAA,EAC5C;AACF;AAUA,IAAM,cAAc,CAAC,WAAmB;AACtC,MAAI,cAAc,KAAK,IAAI;AAAM,kBAAc,MAAM;AACrD,gBAAc,WAAW,QAAQ,KAAK,IAAI,IAAI,GAAM;AACtD;AAaO,IAAM,gBAAgB,OAC3B,OACA,QACA,YAC8B;AAC9B,QAAM,CAAE,cAAc,gBAAiB,IAAI,MAAM,MAAM;AAAA,IACrD;AAAA,IACA;AAAA,MACE,CAAC,QAAQ,mBAAmB,KAAK,EAAE,KAAK,GAAG;AAAA,MAC3C,CAAC,QAAQ,mBAAmB,EAAE,KAAK,GAAG;AAAA,IACxC;AAAA,IACA;AAAA,EACF;AAEA,MAAI,cAA2B;AAC/B,eAAa,IAAI,CAAC,cAAc,UAAU;AACxC,QAAI,cAAc;AAChB,kBAAY,QAAQ,KAAK,CAAC;AAC1B,oBAAc,QAAQ,KAAK;AAAA,IAC7B;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA,mBAAmB,qBAAqB;AAAA,EAC1C;AACF;AAUO,IAAM,sBAAsB,CACjC,OACA,QACA,CAAC,mBAAmB,gBAAgB,GACpC,cACsB;AAEtB,MAAI,iBAAiB,aAAa;AAChC,sBAAkB,UAAU;AAC5B,sBAAkB,YAAY;AAC9B,sBAAkB,SAAS;AAC3B,sBAAkB,cAAc,iBAAiB;AAAA,EACnD;AAEA,MAAI,iBAAiB,mBAAmB;AACtC,UAAM,gBAAgB,iBAAiB,OAAO,QAAQ,SAAS;AAC/D,sBAAkB,UAAU,QAAQ,IAAI;AAAA,MACtC,kBAAkB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAOO,IAAM,wBAAwB,CAAC,gBAA2C;AAC/E,SAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,IACP,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS,QAAQ,QAAQ;AAAA,IACzB,QAAQ;AAAA,IACR;AAAA,EACF;AACF;;;AC7BO,IAAe,YAAf,MAAmD;AAAA,EACrC;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEnB,YAAY,QAAmC;AAC7C,SAAK,MAAM,OAAO;AAClB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO,WAAW;AACjC,SAAK,SAAS,OAAO,UAAU;AAE/B,SAAK,mBAAmB,OAAO,oBAAoB;AACnD,SAAK,oBAAoB,OAAO,qBAAqB;AAErD,SAAK,eAAgB,WAAW,KAAK,MAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,eAAe,CAAC,EAAE;AACxF,SAAK,YAAY,OAAO,YACpB,IAAI,UAAU;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf,CAAC,IACC;AAEJ,QAAI,OAAO,0BAA0B,KAAK;AACxC,WAAK,IAAI,QAAQ,IAAI,MAAM,OAAO,cAAc;AAAA,IAClD,WAAW,OAAO,mBAAmB,QAAW;AAC9C,WAAK,IAAI,QAAQ,IAAI,MAAM,oBAAI,IAAI,CAAC;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsCO,QAAQ,OACb,YACA,QAC+B;AAE/B,QAAI,YAAiB;AACrB,QAAI;AACF,YAAM,WAAW,KAAK,qBAAqB,YAAY,GAAG;AAC1D,YAAM,EAAE,eAAe,aAAa,IAAI,KAAK,aAAa,QAAQ;AAClE,kBAAY;AAEZ,YAAM,gBAAgB,MAAM,QAAQ,KAAK,aAAa;AACtD,YAAM,gBAAgB,KAAK,gBAAgB,eAAe,YAAY,GAAG;AACzE,aAAO;AAAA,IACT,UAAE;AACA,UAAI,WAAW;AACb,qBAAa,SAAS;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBO,kBAAkB,OAOvB,YAKA,YAC+B;AAC/B,QAAI,WAAW,GAAG;AAChB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,QAAI;AAEJ,UAAM,WAAW,KAAK,IAAI,IAAI;AAC9B,WAAO,MAAM;AACX,YAAM,MAAM,KAAK,MAAM,UAAU;AACjC,UAAI,IAAI,SAAS;AACf;AAAA,MACF;AACA,UAAI,IAAI,UAAU,GAAG;AACnB,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC1C;AAEA,YAAM,OAAO,KAAK,IAAI,IAAI,OAAO,QAAQ,IAAI,KAAK,IAAI;AACtD,YAAM,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,IAAI,CAAC;AAE5C,UAAI,KAAK,IAAI,IAAI,UAAU;AACzB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEO,kBAAkB,OAAO,eAAuB;AACrD,UAAM,UAAU,CAAC,KAAK,QAAQ,UAAU,EAAE,KAAK,GAAG;AAClD,UAAM,KAAK,QAAQ,EAAE,YAAY,KAAK,KAAK,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,eAAe,OAAO,eAGvB;AACJ,UAAM,UAAU,CAAC,KAAK,QAAQ,UAAU,EAAE,KAAK,GAAG;AAElD,WAAO,MAAM,KAAK,QAAQ,EAAE,aAAa,KAAK,KAAK,OAAO;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcQ,uBAAuB,OAC7B,YACA,QAC+B;AAC/B,UAAM,MAAM,KAAK,OAAO,UAAU;AAClC,UAAM,iBAAiB,KAAK,kBAAkB,YAAY,GAAG;AAE7D,UAAM,cAAc,mBAAmB,cAAc;AAErD,UAAM,SAAuB,cAAc,CAAC,sBAAsB,WAAW,GAAG,EAAE,aAAa,mBAAmB,MAAM,CAAC,IAAK,MAAM,QAAQ,IAAI;AAAA,MAC9I,KAAK,QAAQ,EAAE,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,MAC7C,KAAK,mBACD,cAAc,KAAK,cAAc,KAAK,QAAQ,cAAc,IAC5D,EAAE,aAAa,QAAW,mBAAmB,MAAM;AAAA,IACzD,CAAC;AAED,WAAO,oBAAoB,KAAK,cAAc,KAAK,QAAQ,QAAQ,KAAK,iBAAiB;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,eAAe,CAAC,aAAyC;AAC/D,QAAI,eAAoB;AACxB,UAAM,gBAAmD,CAAC,QAAQ;AAElE,QAAI,KAAK,UAAU,GAAG;AACpB,YAAM,kBAAkB,IAAI,QAA2B,CAAC,YAAY;AAClE,uBAAe,WAAW,MAAM;AAC9B,kBAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,YACP,WAAW;AAAA,YACX,OAAO;AAAA,YACP,SAAS,QAAQ,QAAQ;AAAA,YACzB,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,GAAG,KAAK,OAAO;AAAA,MACjB,CAAC;AACD,oBAAc,KAAK,eAAe;AAAA,IACpC;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,kBAAkB,CACxB,mBACA,YACA,QACG;AACH,QAAI,KAAK,WAAW;AAClB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,UAAU,WAAW,GAAG,IAAI;AACnD,cAAM,aAAa,KAAK,UACrB,OAAO;AAAA,UACN,YAAY,kBAAkB,WAAW,aACrC,kBAAkB,cAClB;AAAA,UACJ,MAAM,KAAK,IAAI;AAAA,UACf,SAAS,kBAAkB,WAAW,aAClC,WACA,kBAAkB;AAAA,UACtB,GAAG;AAAA,QACL,CAAC,EACA,MAAM,CAAC,UAAU;AAChB,cAAI,eAAe;AACnB,cAAI,GAAG,KAAK,GAAG,SAAS,WAAW,GAAG;AACpC,2BAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUjB;AACA,kBAAQ,KAAK,cAAc,KAAK;AAAA,QAClC,CAAC;AACH,0BAAkB,UAAU,QAAQ,IAAI,CAAC,kBAAkB,SAAS,UAAU,CAAC;AAAA,MACjF,SAAS,OAAO;AACd,gBAAQ,KAAK,8BAA8B,KAAK;AAAA,MAClD;AAAC;AAAA,IACH;AAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEQ,SAAS,CAAC,eAA+B;AAC/C,WAAO,CAAC,KAAK,QAAQ,UAAU,EAAE,KAAK,GAAG;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,oBAAoB,CAC1B,YACA,QACa;AACb,UAAM,UAAU,CAAC,YAAY,KAAK,IAAI,KAAK,WAAW,KAAK,OAAO;AAClE,WAAQ,QAAqB,OAAO,OAAO;AAAA,EAC7C;AACF;;;AC7YA,SAAS,WAAmB;AAC1B,MAAI,SAAS;AACb,QAAM,aAAa;AACnB,QAAM,mBAAmB,WAAW;AACpC,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAU,WAAW,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,CAAC;AAAA,EAC1E;AACA,SAAO;AACT;AAgFO,IAAM,uBAAN,cAAmC,UAA8B;AAAA;AAAA;AAAA;AAAA,EAItE,YAAY,QAAoC;AAC9C,UAAM;AAAA,MACJ,QAAQ,OAAO;AAAA,MACf,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,MAChB,WAAW,OAAO;AAAA,MAClB,KAAK;AAAA,QACH,gBAAgB,OAAO,MAAM,IAAI,YAAU;AAAA,UACzC;AAAA,QACF,EAAE;AAAA,QACF,OAAO,OAAO,iBAAiB,IAAI,MAAM,OAAO,cAAc,IAAI;AAAA,MACpE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,OAAO,YAIL,QAIA,QAC+B;AAC/B,UAAM,iBAAiB,GAAG,MAAM;AAEhC,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAyB,YAAoB,MAAe;AACtE,YAAI,IAAI,OAAO;AACb,gBAAM,EAAE,SAAS,OAAAC,OAAM,IAAI,IAAI,MAAM,UAAU,UAAU;AACzD,cAAI,SAAS;AACX,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAOA;AAAA,cACP,SAAS,QAAQ,QAAQ;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,cAAM,YAAY,SAAS;AAC3B,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AACzC,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,MAAsD,IAAI,eAAe,IAAI,CAAC,mBAAmB;AAAA,UACrG,OAAO,cAAc;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,YACA,QAAQ,YAAY,YAAY;AAAA,YAChC,CAAC,GAAG;AAAA,YACJ,CAAC,WAAW,gBAAgB,WAAW;AAAA,UACzC;AAAA,QACF,EAAE;AAGF,cAAM,gBAAgB,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAEjE,cAAM,aAAa,cAAc,OAAO,CAAC,WAAmB,WAAW,UAAU;AAC/E,cAAI,cAAc;AAClB,cAAI,QAAQ,GAAG;AACb,0BAAc,OAAO,SAAS,SAAS;AAAA,UACzC;AAEA,iBAAO,YAAY;AAAA,QACrB,GAAG,CAAC;AAEJ,cAAM,YAAY,SAAS;AAK3B,uBAAe,OAAO;AACpB,gBAAM,gBAAgB,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAEjE,gBAAM,SAAS,CAAC,GAAG,IAAI;AAAA,YACrB,cAAc,KAAK,EAChB,OAAO,CAAC,KAAe,MAAM,UAAU;AACtC,kBAAI,QAAQ,MAAM,GAAG;AACnB,oBAAI,KAAK,IAAI;AAAA,cACf;AACA,qBAAO;AAAA,YACT,GAAG,CAAC,CAAC;AAAA,UACT,EAAE,OAAO,CAAC;AAEV,qBAAW,MAAM,KAAK;AACpB,kBAAM,sBAAsB,MAAM,GAAG;AACrC,kBAAM,eAAe,oBAAoB;AAAA,cACvC,CAAC,WAAmB,WAAW,UAAU;AACvC,oBAAI,cAAc;AAClB,oBAAI,QAAQ,GAAG;AACb,gCAAc,OAAO,SAAS,SAAS;AAAA,gBACzC;AAEA,uBAAO,YAAY;AAAA,cACrB;AAAA,cACA;AAAA,YACF;AAEA,kBAAM,eAAe,MAAM,GAAG;AAC9B,kBAAM,QAAQ,aAAa,OAAO,CAAC,KAAe,WAAW,UAAU;AACrE,kBAAI,QAAQ,MAAM,GAAG;AACnB,oBAAI,KAAK,SAAS;AAAA,cACpB;AACA,qBAAO;AAAA,YACT,GAAG,CAAC,CAAC;AAKL,gBAAI,gBAAgB,QAAQ;AAC1B;AAAA,YACF;AACA,kBAAM,OAAO,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,SAAS,EAAE,CAAC;AAItD,gBAAI,KAAK,WAAW,GAAG;AACrB;AAAA,YACF;AAEA,uBAAWC,cAAa,MAAM;AAC5B,oBAAM,GAAG,MAAM,KAAK,KAAK,EAAE,CAACA,UAAS,GAAG,YAAY,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,QACF;AAMA,cAAM,UAAU,YAAY;AAC5B,cAAM,SAAS,SAAS,KAAK;AAE7B,YAAI,IAAI,SAAS,CAAC,SAAS;AACzB,cAAI,MAAM,WAAW,YAAY,KAAK;AAAA,QACxC;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,SAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAyB,YAAoB;AAC9D,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AAEzC,cAAM,MAAsD,IAAI,eAAe,IAAI,CAAC,mBAAmB;AAAA,UACrG,OAAO,cAAc;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,YACA,QAAQ,YAAY,YAAY;AAAA,YAChC,CAAC,GAAG;AAAA,YACJ,CAAC,IAAI;AAAA,UACP;AAAA,QACF,EAAE;AAGF,cAAM,gBAAgB,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AACjE,cAAM,aAAa,cAAc,OAAO,CAAC,WAAmB,WAAW,UAAU;AAC/E,cAAI,cAAc;AAClB,cAAI,QAAQ,GAAG;AACb,0BAAc,OAAO,SAAS,SAAS;AAAA,UACzC;AAEA,iBAAO,YAAY;AAAA,QACrB,GAAG,CAAC;AAEJ,eAAO;AAAA,UACL,WAAW,KAAK,IAAI,GAAG,SAAS,UAAU;AAAA,UAC1C,QAAQ,SAAS,KAAK;AAAA,QACxB;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAyB,YAAoB;AAC7D,cAAM,UAAU,CAAC,YAAY,GAAG,EAAE,KAAK,GAAG;AAC1C,YAAI,IAAI,OAAO;AACb,cAAI,MAAM,IAAI,UAAU;AAAA,QAC1B;AAEA,cAAM,QAAQ,IAAI,IAAI,eAAe,IAAI,CAAC,kBAAkB;AAC1D;AAAA,YACE;AAAA,YACA;AAAA,YACA,CAAC,OAAO;AAAA,YACR,CAAC,IAAI;AAAA,UACP;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,cAIL,QAIA,QAC+B;AAC/B,UAAM,aAAa,GAAG,MAAM;AAE5B,UAAM,iBAAiB,GAAG,MAAM;AAEhC,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAyB,YAAoB,MAAe;AACtE,YAAI,IAAI,OAAO;AACb,gBAAM,EAAE,SAAS,OAAAD,OAAM,IAAI,IAAI,MAAM,UAAU,UAAU;AACzD,cAAI,SAAS;AACX,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAOA;AAAA,cACP,SAAS,QAAQ,QAAQ;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,cAAM,YAAY,SAAS;AAC3B,cAAM,MAAM,KAAK,IAAI;AAErB,cAAM,gBAAgB,KAAK,MAAM,MAAM,UAAU;AACjD,cAAM,aAAa,CAAC,YAAY,aAAa,EAAE,KAAK,GAAG;AACvD,cAAM,iBAAiB,gBAAgB;AACvC,cAAM,cAAc,CAAC,YAAY,cAAc,EAAE,KAAK,GAAG;AACzD,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,MAAM,IAAI,eAAe,IAAI,CAAC,mBAAmB;AAAA,UACrD,OAAO,cAAc;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,YACA,QAAQ,YAAY,cAAc;AAAA,YAClC,CAAC,YAAY,WAAW;AAAA,YACxB,CAAC,QAAQ,KAAK,gBAAgB,WAAW,WAAW;AAAA;AAAA,UAEtD;AAAA,QACF,EAAE;AAEF,cAAM,sBAAuB,MAAM,iBAAkB;AACrD,cAAM,CAAC,SAAS,UAAU,OAAO,IAAI,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAIhF,YAAI,SAAS;AACX,kBAAQ,KAAK,WAAW,YAAY,SAAS,CAAC;AAAA,QAChD;AAEA,cAAM,qBAAqB,SAAS,OAAO,CAAC,WAAmB,WAAW,UAAU;AAClF,cAAI,cAAc;AAClB,cAAI,QAAQ,GAAG;AACb,0BAAc,OAAO,SAAS,SAAS;AAAA,UACzC;AAEA,iBAAO,YAAY;AAAA,QACrB,GAAG,CAAC;AAEJ,cAAM,oBAAoB,QAAQ,OAAO,CAAC,WAAmB,WAAW,UAAU;AAChF,cAAI,cAAc;AAClB,cAAI,QAAQ,GAAG;AACb,0BAAc,OAAO,SAAS,SAAS;AAAA,UACzC;AAEA,iBAAO,YAAY;AAAA,QACrB,GAAG,CAAC;AAEJ,cAAM,sBAAsB,KAAK,KAAK,sBAAsB,IAAI,oBAAoB;AAEpF,cAAM,aAAa,sBAAsB;AAEzC,cAAM,YAAY,SAAS;AAK3B,uBAAe,OAAO;AACpB,gBAAM,MAAM,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAEvD,gBAAM,gBAAgB,CAAC,GAAG,IAAI;AAAA,YAC5B,IACG,QAAQ,CAAC,CAACE,QAAO,MAAMA,QAAO,EAC9B,OAAO,CAAC,KAAe,MAAM,UAAU;AACtC,kBAAI,QAAQ,MAAM,GAAG;AACnB,oBAAI,KAAK,IAAI;AAAA,cACf;AACA,qBAAO;AAAA,YACT,GAAG,CAAC,CAAC;AAAA,UACT,EAAE,OAAO,CAAC;AAEV,qBAAW,MAAM,KAAK;AACpB,kBAAM,CAACA,UAAS,WAAW,QAAQ,IAAI,MAAM,GAAG;AAChD,kBAAM,QAAQA,SAAQ,OAAO,CAAC,KAAe,WAAW,UAAU;AAChE,kBAAI,QAAQ,MAAM,GAAG;AACnB,oBAAI,KAAK,SAAS;AAAA,cACpB;AACA,qBAAO;AAAA,YACT,GAAG,CAAC,CAAC;AAEL,kBAAM,eAAeA,SAAQ,OAAO,CAAC,WAAmB,WAAW,UAAU;AAC3E,kBAAI,cAAc;AAClB,kBAAI,QAAQ,GAAG;AACb,8BAAc,OAAO,SAAS,SAAS;AAAA,cACzC;AAEA,qBAAO,YAAY;AAAA,YACrB,GAAG,CAAC;AAKJ,gBAAI,gBAAgB,QAAQ;AAC1B;AAAA,YACF;AACA,kBAAM,OAAO,cAAc,OAAO,CAAC,OAAO,CAAC,MAAM,SAAS,EAAE,CAAC;AAI7D,gBAAI,KAAK,WAAW,GAAG;AACrB;AAAA,YACF;AAEA,uBAAWD,cAAa,MAAM;AAC5B,oBAAM,GAAG,MAAM,KAAK,YAAY,EAAE,CAACA,UAAS,GAAG,YAAY,CAAC;AAAA,YAC9D;AAAA,UACF;AAAA,QACF;AAGA,cAAM,SAAS,gBAAgB,KAAK;AACpC,YAAI,IAAI,SAAS,CAAC,SAAS;AACzB,cAAI,MAAM,WAAW,YAAY,KAAK;AAAA,QACxC;AACA,eAAO;AAAA,UACL,SAAS,QAAQ,OAAO;AAAA,UACxB,OAAO;AAAA,UACP,WAAW,KAAK,IAAI,GAAG,SAAS;AAAA,UAChC;AAAA,UACA,SAAS,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAyB,YAAoB;AAC9D,cAAM,MAAM,KAAK,IAAI;AAErB,cAAM,gBAAgB,KAAK,MAAM,MAAM,UAAU;AACjD,cAAM,aAAa,CAAC,YAAY,aAAa,EAAE,KAAK,GAAG;AACvD,cAAM,iBAAiB,gBAAgB;AACvC,cAAM,cAAc,CAAC,YAAY,cAAc,EAAE,KAAK,GAAG;AAEzD,cAAM,MAAM,IAAI,eAAe,IAAI,CAAC,mBAAmB;AAAA,UACrD,OAAO,cAAc;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,YACA,QAAQ,YAAY,cAAc;AAAA,YAClC,CAAC,YAAY,WAAW;AAAA,YACxB,CAAC,KAAK,UAAU;AAAA;AAAA,UAElB;AAAA,QACF,EAAE;AAEF,cAAM,aAAa,MAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAC9D,eAAO;AAAA,UACL,WAAW,KAAK,IAAI,GAAG,SAAS,UAAU;AAAA,UAC1C,QAAQ,gBAAgB,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAyB,YAAoB;AAC7D,cAAM,UAAU,CAAC,YAAY,GAAG,EAAE,KAAK,GAAG;AAC1C,YAAI,IAAI,OAAO;AACb,cAAI,MAAM,IAAI,UAAU;AAAA,QAC1B;AAGA,cAAM,QAAQ,IAAI,IAAI,eAAe,IAAI,CAAC,kBAAkB;AAC1D;AAAA,YACE;AAAA,YACA;AAAA,YACA,CAAC,OAAO;AAAA,YACR,CAAC,IAAI;AAAA,UACP;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;;;ACraO,IAAM,kBAAN,cAA8B,UAAyB;AAAA;AAAA;AAAA;AAAA,EAK5D,YAAY,QAA+B;AACzC,UAAM;AAAA,MACJ,QAAQ,OAAO;AAAA,MACf,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,MAChB,WAAW,OAAO;AAAA,MAClB,KAAK;AAAA,QACH,OAAO,OAAO;AAAA,MAChB;AAAA,MACA,gBAAgB,OAAO;AAAA,MACvB,kBAAkB,OAAO;AAAA,MACzB,mBAAmB,OAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,OAAO,YAIL,QAIA,QAC0B;AAC1B,UAAM,iBAAiB,GAAG,MAAM;AAChC,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAoB,YAAoB,MAAe;AACjE,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AACzC,YAAI,IAAI,OAAO;AACb,gBAAM,EAAE,SAAS,OAAAE,OAAM,IAAI,IAAI,MAAM,UAAU,UAAU;AACzD,cAAI,SAAS;AACX,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAOA;AAAA,cACP,SAAS,QAAQ,QAAQ;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,wBAAwB,MAAM;AAAA,UAClC;AAAA,UACA,QAAQ,aAAa,YAAY;AAAA,UACjC,CAAC,GAAG;AAAA,UACJ,CAAC,gBAAgB,WAAW;AAAA,QAC9B;AAEA,cAAM,UAAU,yBAAyB;AAEzC,cAAM,kBAAkB,KAAK,IAAI,GAAG,SAAS,qBAAqB;AAElE,cAAM,SAAS,SAAS,KAAK;AAC7B,YAAI,IAAI,SAAS,CAAC,SAAS;AACzB,cAAI,MAAM,WAAW,YAAY,KAAK;AAAA,QACxC;AAEA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,UACP,WAAW;AAAA,UACX;AAAA,UACA,SAAS,QAAQ,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAoB,YAAoB;AACzD,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AAEzC,cAAM,aAAa,MAAM;AAAA,UACvB;AAAA,UACA,QAAQ,aAAa,YAAY;AAAA,UACjC,CAAC,GAAG;AAAA,UACJ,CAAC,IAAI;AAAA,QACP;AAEA,eAAO;AAAA,UACL,WAAW,KAAK,IAAI,GAAG,SAAS,UAAU;AAAA,UAC1C,QAAQ,SAAS,KAAK;AAAA,QACxB;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAoB,YAAoB;AACxD,cAAM,UAAU,CAAC,YAAY,GAAG,EAAE,KAAK,GAAG;AAC1C,YAAI,IAAI,OAAO;AACb,cAAI,MAAM,IAAI,UAAU;AAAA,QAC1B;AAEA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,CAAC,OAAO;AAAA,UACR,CAAC,IAAI;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,cAIL,QAIA,QAC0B;AAC1B,UAAM,aAAa,GAAG,MAAM;AAC5B,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAoB,YAAoB,MAAe;AACjE,cAAM,MAAM,KAAK,IAAI;AAErB,cAAM,gBAAgB,KAAK,MAAM,MAAM,UAAU;AACjD,cAAM,aAAa,CAAC,YAAY,aAAa,EAAE,KAAK,GAAG;AACvD,cAAM,iBAAiB,gBAAgB;AACvC,cAAM,cAAc,CAAC,YAAY,cAAc,EAAE,KAAK,GAAG;AAEzD,YAAI,IAAI,OAAO;AACb,gBAAM,EAAE,SAAS,OAAAA,OAAM,IAAI,IAAI,MAAM,UAAU,UAAU;AACzD,cAAI,SAAS;AACX,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAOA;AAAA,cACP,SAAS,QAAQ,QAAQ;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,kBAAkB,MAAM;AAAA,UAC5B;AAAA,UACA,QAAQ,aAAa,cAAc;AAAA,UACnC,CAAC,YAAY,WAAW;AAAA,UACxB,CAAC,QAAQ,KAAK,YAAY,WAAW;AAAA,QACvC;AAEA,cAAM,UAAU,mBAAmB;AAEnC,cAAM,SAAS,gBAAgB,KAAK;AACpC,YAAI,IAAI,SAAS,CAAC,SAAS;AACzB,cAAI,MAAM,WAAW,YAAY,KAAK;AAAA,QACxC;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,UACP,WAAW,KAAK,IAAI,GAAG,eAAe;AAAA,UACtC;AAAA,UACA,SAAS,QAAQ,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAoB,YAAoB;AACzD,cAAM,MAAM,KAAK,IAAI;AACrB,cAAM,gBAAgB,KAAK,MAAM,MAAM,UAAU;AACjD,cAAM,aAAa,CAAC,YAAY,aAAa,EAAE,KAAK,GAAG;AACvD,cAAM,iBAAiB,gBAAgB;AACvC,cAAM,cAAc,CAAC,YAAY,cAAc,EAAE,KAAK,GAAG;AAEzD,cAAM,aAAa,MAAM;AAAA,UACvB;AAAA,UACA,QAAQ,aAAa,cAAc;AAAA,UACnC,CAAC,YAAY,WAAW;AAAA,UACxB,CAAC,KAAK,UAAU;AAAA,QAClB;AAEA,eAAO;AAAA,UACL,WAAW,KAAK,IAAI,GAAG,SAAS,UAAU;AAAA,UAC1C,QAAQ,gBAAgB,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAoB,YAAoB;AACxD,cAAM,UAAU,CAAC,YAAY,GAAG,EAAE,KAAK,GAAG;AAC1C,YAAI,IAAI,OAAO;AACb,cAAI,MAAM,IAAI,UAAU;AAAA,QAC1B;AAEA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,CAAC,OAAO;AAAA,UACR,CAAC,IAAI;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,OAAO,YAML,YAIA,UAMA,WAC0B;AAC1B,UAAM,mBAAmB,GAAG,QAAQ;AACpC,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAoB,YAAoB,MAAe;AACjE,YAAI,IAAI,OAAO;AACb,gBAAM,EAAE,SAAS,OAAAA,OAAM,IAAI,IAAI,MAAM,UAAU,UAAU;AACzD,cAAI,SAAS;AACX,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,cACX,OAAOA;AAAA,cACP,SAAS,QAAQ,QAAQ;AAAA,cACzB,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,cAAM,MAAM,KAAK,IAAI;AAErB,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,CAAC,WAAW,KAAK,IAAI,MAAM;AAAA,UAC/B;AAAA,UACA,QAAQ,aAAa,YAAY;AAAA,UACjC,CAAC,UAAU;AAAA,UACX,CAAC,WAAW,kBAAkB,YAAY,KAAK,WAAW;AAAA,QAC5D;AAEA,cAAM,UAAU,aAAa;AAC7B,YAAI,IAAI,SAAS,CAAC,SAAS;AACzB,cAAI,MAAM,WAAW,YAAY,KAAK;AAAA,QACxC;AAEA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,SAAS,QAAQ,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAoB,YAAoB;AAEzD,cAAM,CAAC,iBAAiB,UAAU,IAAI,MAAM;AAAA,UAC1C;AAAA,UACA,QAAQ,aAAa,YAAY;AAAA,UACjC,CAAC,UAAU;AAAA,UACX,CAAC,SAAS;AAAA,QACZ;AAEA,cAAM,gBAAgB,KAAK,IAAI,IAAI;AACnC,cAAM,sBAAsB,aAAa;AAEzC,eAAO;AAAA,UACL,WAAW;AAAA,UACX,OAAO,eAAe,gCAAgC,gBAAgB;AAAA,QACxE;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAoB,YAAoB;AACxD,cAAM,UAAU;AAChB,YAAI,IAAI,OAAO;AACb,cAAI,MAAM,IAAI,UAAU;AAAA,QAC1B;AAEA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,CAAC,OAAO;AAAA,UACR,CAAC,IAAI;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,OAAO,kBAIL,QAIA,QAC0B;AAC1B,UAAM,iBAAiB,GAAG,MAAM;AAEhC,WAAO,OAAO;AAAA,MACZ,MAAM,MAAM,KAAoB,YAAoB,MAAe;AACjE,YAAI,CAAC,IAAI,OAAO;AACd,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AACA,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AACzC,cAAM,SAAS,SAAS,KAAK;AAC7B,cAAM,cAAc,OAAO,KAAK,IAAI,GAAG,IAAI,IAAI;AAE/C,cAAM,MAAM,OAAO,IAAI,MAAM,IAAI,GAAG,MAAM;AAC1C,YAAI,KAAK;AACP,gBAAM,0BAA0B,IAAI,MAAM,KAAK,GAAG;AAClD,gBAAM,UAAU,0BAA0B;AAE1C,gBAAM,UAAU,UACZ;AAAA,YACA;AAAA,YACA,QAAQ,aAAa,kBAAkB;AAAA,YACvC,CAAC,GAAG;AAAA,YACJ,CAAC,gBAAgB,WAAW;AAAA,UAC9B,IACE,QAAQ,QAAQ;AAEpB,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,YACP,WAAW,SAAS;AAAA,YACpB;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,cAAM,wBAAwB,MAAM;AAAA,UAClC;AAAA,UACA,QAAQ,aAAa,kBAAkB;AAAA,UACvC,CAAC,GAAG;AAAA,UACJ,CAAC,gBAAgB,WAAW;AAAA,QAC9B;AACA,YAAI,MAAM,IAAI,KAAK,qBAAqB;AACxC,cAAM,YAAY,SAAS;AAE3B,eAAO;AAAA,UACL,SAAS,aAAa;AAAA,UACtB,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,SAAS,QAAQ,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,MAAM,aAAa,KAAoB,YAAoB;AACzD,YAAI,CAAC,IAAI,OAAO;AACd,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AAEA,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AAEzC,cAAM,MAAM,OAAO,IAAI,MAAM,IAAI,GAAG,MAAM;AAC1C,YAAI,KAAK;AACP,gBAAM,mBAAmB,IAAI,MAAM,IAAI,GAAG,KAAK;AAC/C,iBAAO;AAAA,YACL,WAAW,KAAK,IAAI,GAAG,SAAS,gBAAgB;AAAA,YAChD,QAAQ,SAAS,KAAK;AAAA,UACxB;AAAA,QACF;AAEA,cAAM,aAAa,MAAM;AAAA,UACvB;AAAA,UACA,QAAQ,aAAa,kBAAkB;AAAA,UACvC,CAAC,GAAG;AAAA,UACJ,CAAC,IAAI;AAAA,QACP;AACA,eAAO;AAAA,UACL,WAAW,KAAK,IAAI,GAAG,SAAS,UAAU;AAAA,UAC1C,QAAQ,SAAS,KAAK;AAAA,QACxB;AAAA,MACF;AAAA,MACA,MAAM,YAAY,KAAoB,YAAoB;AAExD,YAAI,CAAC,IAAI,OAAO;AACd,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AAEA,cAAM,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,cAAc;AACrD,cAAM,MAAM,CAAC,YAAY,MAAM,EAAE,KAAK,GAAG;AACzC,YAAI,MAAM,IAAI,GAAG;AAEjB,cAAM,UAAU,CAAC,YAAY,GAAG,EAAE,KAAK,GAAG;AAE1C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,CAAC,OAAO;AAAA,UACR,CAAC,IAAI;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": ["fixedWindowLimitScript", "fixedWindowRemainingTokensScript", "slidingWindowLimitScript", "slidingWindowRemainingTokensScript", "fixedWindowLimitScript", "fixedWindowRemainingTokensScript", "slidingWindowLimitScript", "slidingWindowRemainingTokensScript", "reset", "requestId", "current", "reset"]}