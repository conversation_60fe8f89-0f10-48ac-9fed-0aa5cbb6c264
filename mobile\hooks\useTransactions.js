import {useCallback, useState} from 'react';
import { Alert } from 'react-native';


const API_URL = 'http://localhost:5001/api';


export const useTransactions = (userId) => {
  const [transactions, setTransactions] = useState([]);
  const [summary, setSummary] = useState({
    balance: 0, 
    income: 0, 
    expense: 0
});
const [isLoading, setIsLoading] = useState(true);


//useCallback is used to memoize the function and avoid unnecessary re-renders
const fetchTransactions = useCallback(async () => {
  try {
    const response = await fetch(`${API_URL}/transactions/${userId}`);
    const data = await response.json();
    setTransactions(data);
  } catch (error) {
    console.error('Error fetching transactions', error);
  }
},[userId]);

const fetchSummary = useCallback(async () => {
  try {
    const response = await fetch(`${API_URL}/transactions/summary/${userId}`);
    const data = await response.json();
    setSummary(data);
  } catch (error) {
    console.error('Error fetching summary', error);
  }
},[userId]);

const loadData = useCallback(async () => {
  setIsLoading(true);

  await Promise.all([fetchTransactions(),fetchSummary()]);
  setIsLoading(false);
},  [fetchTransactions, fetchSummary]);


const deleteTransaction = async (id) => {
  try {
    const response = await fetch(`${API_URL}/transactions/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete transaction');
    }

    await loadData();
    Alert.alert('Transaction deleted successfully');
  } catch (error) {
    console.error('Error deleting transaction', error);
    Alert.alert('Failed to delete transaction');
  }
};
return {
  transactions,
  summary,
  isLoading,
  deleteTransaction,
  loadData,
};
};


